<template>
  <div id="potentialApply" class="potential">
    <TY_NavTop title="潜在客户申请"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="panel-content def-gap">
        <div>访谈记录</div>
      </div>
      <div class="panel-content def-gap">
        <el-row type="flex" justify="space-between">
          <el-col :span="8">访谈对象</el-col>
          <el-col :span="14">
            <div v-if="(interView.interviewer || '') !== ''">
              共{{interView.interviewerList.length}}人：{{interView.interviewerNames | handleNull | formatName}}
            </div>
            <div v-else class="conEmpty">尚未选择</div>
          </el-col>
          <el-col :span="2"><el-link type="primary" @click="chooseContact(1)">选择</el-link></el-col>
        </el-row>
        <el-row>
          <el-col :span="6">访谈日期</el-col>
          <el-col :span="18">
            <el-date-picker
              v-model="interView.interviewDate"
              type="date"
              placeholder="请选择"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              size="mini">
            </el-date-picker>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">同行者（同事）</el-col>
          <el-col :span="14">
            <div v-if="interView.tsIds && interView.tsIds !== ''">
              共{{interView.tsList.length}}人：{{interView.tsNames}}
            </div>
            <div v-else class="conEmpty">尚未选择</div>
          </el-col>
          <el-col :span="2"><el-link type="primary" @click="chooseContact(2)">选择</el-link></el-col>
        </el-row>
        <el-row>
          <el-col :span="8">同行者（非同事）</el-col>
          <el-col :span="14">
            <div v-if="interView.ftxIds && interView.ftxIds !== ''">
              共{{interView.ftsList.length}}人：{{interView.ftxNames}}
            </div>
            <div v-else class="conEmpty">尚未选择</div>
          </el-col>
          <el-col :span="2"><el-link type="primary" @click="chooseContact(3)">选择</el-link></el-col>
        </el-row>
      </div>
      <div class="panel-content def-gap">
        <el-row>
          <el-col :span="8">访谈目标</el-col>
          <el-col :span="14">
            <div v-if="interView.mbContent === ''" class="conEmpty">尚未编辑</div>
          </el-col>
          <el-col :span="2"><el-link type="primary" @click="interViewContent(1)">编辑</el-link></el-col>
        </el-row>
        <el-row class="blueTip" v-if="interView.mbContent === ''">注：如必要，或公司有要求，请点击“编辑”！</el-row>
        <el-row v-else>{{ interView.mbContent }}</el-row>
      </div>
      <div class="panel-content def-gap">
        <el-row>
          <el-col :span="8">谈话要点</el-col>
          <el-col :span="14">
            <div v-if="interView.thyd.length === 0" class="conEmpty">尚未编辑</div>
          </el-col>
          <el-col :span="2"><el-link type="primary" @click="interViewContent(2, 1)">新增</el-link></el-col>
        </el-row>
        <el-row class="blueTip" v-if="interView.thyd.length === 0">注：如某些访谈对象或同行者发言较重要，请点击“新增”！</el-row>
        <el-row v-else v-for="(participat, index) in interView.thyd" :key="index">
          <el-col :span="6">{{ participat.participatorName || '--' }}</el-col>
          <el-col :span="12">{{ participat.content }}</el-col>
          <el-col :span="4"><el-link type="primary" @click="interViewContent(2, 2, index)">修改</el-link></el-col>
          <el-col :span="2"><el-link type="danger" @click="delPot(2, index)">删除</el-link></el-col>
        </el-row>
      </div>
      <div class="panel-content def-gap">
        <el-row>
          <el-col :span="8">成果/结论</el-col>
          <el-col :span="14">
            <div v-if="interView.cgContent.length === 0" class="conEmpty">尚未编辑</div>
          </el-col>
          <el-col :span="2"><el-link type="primary" @click="interViewContent(3, 1)">新增</el-link></el-col>
        </el-row>
        <el-row class="blueTip" v-if="interView.cgContent.length === 0">注：如本次访谈有成果或结论，或公司有要求，请点击“新增”！</el-row>
        <el-row v-else v-for="(cgContent, index) in interView.cgContent" :key="index">
          <el-col :span="18">{{ cgContent }}</el-col>
          <el-col :span="4"><el-link type="primary" @click="interViewContent(3, 2, index)">修改</el-link></el-col>
          <el-col :span="2"><el-link type="danger" @click="delPot(3, index)">删除</el-link></el-col>
        </el-row>
      </div>
      <div class="handle_button handle-center">
        <el-button type="primary" :disabled="editControl" @click="interviewUpdateSure" size="medium">提交</el-button>
      </div>
    </div>
    <contacts :potentialId="potentialId" :personnelList="personnelList" :source="source" v-bind:show.sync="bookVisible" @select_contact="setInterview"></contacts>
    <interviewCon
      :personnelListD="participatList"
      :participator="participator"
      :content="content"
      :source="source"
      :editType="editType"
      v-bind:show.sync="contentVisible"
      @select_content="setInterviewContent"></interviewCon>
  </div>
</template>
<style lang="less">
  .potential {
    .bold {  font-weight: bold;  }
    .blueTip{ color: #0070bf;}
  }
  .interviewerText{
    padding: 0 20px;
    height: 28px;
    line-height: 28px;
    border: 1px solid #dcdfe6;
  }
  .interviewerText:hover {
    border: 1px solid #ccc;
  }
  .conEmpty{
    text-align: center;
  }
</style>
<script>
import Contacts from '../potentialCustomer/contactList'
import InterviewCon from '../potentialCustomer/interviewContent'
import { formatDate, gohistory, handleNull, formatName } from '../../../js/common'
export default {
  name: 'viewDetailUpdate',
  filters: {
    formatDate,
    gohistory,
    formatName,
    handleNull
  },
  data () {
    return {
      loading: true,
      editType: '',
      content: '',
      participator: '',
      interviewInfo: {
      },
      personnelList: [],
      participatList: [],
      interView: {
        interviewer: '',
        tsIds: '',
        ftxIds: '',
        interviewDate: '',
        content: '',
        mbContent: '',
        tsList: [],
        interviewerList: [],
        thyd: [],
        cgContent: [],
        ftsList: []
      },
      orgInterView: {
        interviewer: '',
        tsIds: '',
        ftxIds: '',
        interviewDate: '',
        content: '',
        mbContent: '',
        tsList: [],
        interviewerList: [],
        thyd: [],
        cgContent: [],
        ftsList: []
      },
      potential: {},
      allStaff: [],
      source: 0,
      bookVisible: false,
      potentialId: '',
      contentVisible: false
    }
  },
  components: {
    'contacts': Contacts,
    'interviewCon': InterviewCon
  },
  computed: {
    editControl () {
      let state = 0, able = false
      let _this = this
      for (let item in _this.interView) {
        if (item === 'interviewDate') {
          if (_this.interView.interviewDate !== '') {
            able = true
          }
          if (_this.interView[item] !== _this.orgInterView[item]) {
            state++
          }
        } else {
          if (item === 'ftsList' || item === 'users' || item === 'interviewerList' || item === 'tsList' || item === 'thyd' || item === 'cgContent') {
            if (_this.interView[item].length > 0) {
              able = true
            }
            if (_this.interView[item].length !== _this.orgInterView[item].length) {
              state++
            } else {
              _this.interView[item].forEach(function (data, icon) {
                if (item === 'cgContent') {
                  let able = _this.orgInterView[item].findIndex(value => value === data)
                  if (able === -1) {
                    state++
                  }
                } else {
                  for(let key in data) {
                    if (data[key] !== _this.orgInterView[item][icon][key]) {
                      state++
                    }
                  }
                }
              })
            }
          } else {
            if (_this.interView[item] !== '') {
              able = true
            }
            if (_this.interView[item] !== _this.orgInterView[item]) {
              state++
            }
          }
        }
      }
      if (able && state > 0) {
        return false
      } else {
        return true
      }
    }
  },
  created () {
    let _this = this
    let detailId = _this.$route.params.detailId
    let type = _this.$route.params.type
    let interviewId = _this.$route.params.interviewId
    _this.potentialId = detailId
    if (type === 'new') {
      _this.loading = false
      let dataStr = JSON.stringify(_this.interView)
      const data1 = JSON.parse(dataStr)
      _this.orgInterView = data1
    } else if (type === 'update') {
      this.axios.post('../../../saleInterview/getInterview.do', {
        id: interviewId
      }).then(function (response) {
        _this.loading = false
        let details = response.data.data
        details.cgContent = handleNull(details.cgContent) ? details.cgContent.split("$a$") : []
        details.interviewDate = formatDate(details.interviewDate, 'yyyy-MM-dd')
        _this.interView = details
        let dataStr = JSON.stringify(details)
        const data1 = JSON.parse(dataStr)
        _this.orgInterView = data1
      }).catch(function (error) {
        console.log(error)
      })
    }
    this.axios.post('../../../sale/getPotentialCustomer.do', {
      id: detailId
    }).then(function (response) {
      _this.loading = false
      let potential = response.data.data
      _this.potential = potential
    }).catch(function (error) {
      console.log(error)
    })
  },
  methods: {
    interviewUpdateSure: function () {
      let _this = this
      _this.loading = true
      let cgContent = ``
      let json = {
        interviewer: _this.interView.interviewer || '',
        tsIds: _this.interView.tsIds || '',
        ftxIds: _this.interView.ftxIds || '',
        interviewDate: _this.interView.interviewDate,
        mbContent: _this.interView.mbContent || '',
        cgContent: ``,
        ftsListJson: JSON.stringify(_this.interView.ftsList || []),
        thydJson: JSON.stringify(_this.interView.thyd || [])
      }
      for(let i =0; i < _this.interView.cgContent.length; i++) {
        if(_this.interView.cgContent[i] !== "") cgContent += _this.interView.cgContent[i] + `$a$`
      }
      if (cgContent !== "") {
        cgContent = cgContent.slice(0, cgContent.length-3);
      }
      json.cgContent = cgContent
      let url = ''
      if (this.$route.params.type === 'new') {
        url = '../../../sales/addCustomerInterview.do'
        json.customer = this.$route.params.detailId
      } else {
        json.id = this.$route.params.interviewId
        json.customer = this.$route.params.detailId
        url = '../../../sales/updateCustomerInterview.do'
      }
      this.axios.post(url, json
      ).then(function () {
        _this.loading = false
        gohistory(_this)
      }).catch(function (error) {
        console.log(error)
      })
    },
    setParticipatList: function () {
      let list = []
      let list1 = this.interView.interviewerList
      let list2 = this.interView.tsList
      let list3 = this.interView.ftsList
      let list4 = this.interView.thyd
      list1.forEach(function (item){
        item.type = 1 // "参与者类型:1-访谈对象，2-同行者(同事)，3-同行者（非同事）"
        let index = list4.findIndex(value => Number(value.participator) === Number(item.participator))
        index > -1 ? item.abled = true : item.abled = false
        list.push(item)
      })
      list2.forEach(function (item){
        item.type = 2
        let index = list4.findIndex(value => Number(value.participator) === Number(item.participator))
        index > -1 ? item.abled = true : item.abled = false
        list.push(item)
      })
      list3.forEach(function (item){
        let index = list4.findIndex(value => Number(value.participator) === Number(item.participator))
        index > -1 ? item.abled = true : item.abled = false
        item.type = 3
        list.push(item)
      })
      this.participatList = list
    },
    chooseContact: function (type) {
      let _this = this
      let list = []
      _this.source = type
      _this.bookVisible = true
      if (type === 1) {
        list = _this.potential.contactsList
      } else if (type === 2) {
        _this.loading = true
        _this.axios.post('../../../org/getPresentUsers.do', {}
        ).then(function (response) {
          _this.loading = false
          list = response.data.data
          _this.allStaff = list
          _this.personnelList = list
        }).catch(function (error) {
          console.log(error)
        })
      } else {
        list = _this.interView.ftsList || []
      }
      _this.personnelList = list
    },
    setInterview: function (contact) {
      let _this = this
      let names = []
      if (_this.source === 1) {
        let list = _this.potential.contactsList
        let arr1 = []
        contact.forEach((id)=> {
          var item = list.find(value => value.id === id);
          if (item) {
            item.memo = item.name
            item.participator = id
            arr1.push(item)
            names.push(item.name || '--')
          }
        })
        _this.interView.interviewer = contact.toString()
        _this.interView.interviewerList = arr1
        _this.interView.interviewerNames = names.toString()
      } else if (_this.source === 2) {
        let arr2 = []
        contact.forEach((id)=> {
          var item = _this.allStaff.find(value => value.userID === id);
          if (item) {
            item.memo = item.userName
            item.participator = id
            arr2.push(item)
            names.push(item.userName)
          }
        })
        _this.interView.tsIds = contact.toString()
        _this.interView.tsList = arr2
        _this.interView.tsNames = names.toString()
      } else if (_this.source === 3) {
        let ftxIds = []
        _this.interView.ftsList = contact
        contact.forEach((item)=> {
          ftxIds.push(item.id)
          names.push(item.name)
        })
        _this.interView.ftxIds = ftxIds.toString()
        _this.interView.ftxNames = names.toString()
      }
      _this.interView.thyd.forEach(function (item, index) {
        let icon = ``
        if (item.type === 1) {
          icon =_this.interView.interviewerList.findIndex(value => Number(value.participator) === Number(item.participator))
        } else {
          icon =_this.interView.tsList.findIndex(value => Number(value.participator) === Number(item.participator))
        }
        if (icon === -1) _this.interView.thyd.splice(index, 1)
      })
    },
    interViewContent: function (source, type, index) {
      if (source === 1) {
        this.content = this.interView.mbContent
      } else if (source === 2) {
        this.setParticipatList()
        this.editType = type
        if (type === 1) {
          this.content = ''
          this.participator = ''
        } else {
          this.content = this.interView.thyd[index].content
          this.participator = this.interView.thyd[index].participator
          let icon = this.participatList.findIndex(value => Number(value.participator) === Number(this.participator))
          this.participatList[icon].abled = false
          this.interviewInfo.thydIndex = index
        }
      } else if (source === 3) {
        this.editType = type
        if (type === 1) {
          this.content = ''
        } else {
          this.content = this.interView.cgContent[index]
          this.interviewInfo.cgContentIndex = index
        }
      }
      this.source = source
      this.contentVisible = true
    },
    setInterviewContent: function (val) {
      let _this = this
      let editType = ''
      if (_this.source === 1) {
        _this.interView.mbContent = val
      } else if (_this.source === 2) {
        editType = this.editType
        if (editType === 1) {
          val.orders = _this.interView.thyd.length + 1
          _this.interView.thyd.push(val)
        } else {
          let index = _this.interviewInfo.thydIndex
          val.orders = _this.interView.thyd[index].orders
          _this.interView.thyd.splice(index, 1, val)
        }
      } else if (_this.source === 3) {
        editType = this.editType
        if (editType === 1) {
          _this.interView.cgContent.push(val)
        } else {
          let index = _this.interviewInfo.cgContentIndex;
          _this.interView.cgContent.splice(index, 1, val)
        }
      }
    },
    delPot: function (source, index) {
      let _this = this
      if (source === 2) {
        _this.interView.thyd.splice(index, 1)
      } else if (source === 3) {
        _this.interView.cgContent.splice(index, 1)
      }
    }
  }
}
</script>
