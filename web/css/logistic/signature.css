.inputSize .form-control{width: 100%;}
.linkBtn {  color: #036bc0;  padding: 0 20px;  cursor: default;  }
.linkUploadify .uploadify-button { margin: 0; background-color: inherit; border: none;  line-height: 12px;  padding: 3px 6px;  font-size: 12px;  color: #1685ea;  cursor: pointer;  text-decoration: none;
    font-weight: normal;  display: inline-block;  vertical-align: middle;  }
.linkUploadify .uploadify-button:active{border: none;}
.imgsthumb{position: relative;  margin: 10px 30px 10px 0;display: inline-block;overflow: hidden;}
.imgsthumb .filePic{  display: inline-block;width:90px;height:70px;  background-size: 100%;  background-position: center center;  }
.imgsthumb span.canclePic{  position: absolute;  top: 0;  right: 0;padding: 2px;  color: red;  cursor: pointer;display: none;  }
.imgsthumb a{  display: none;  background-color: rgba(93, 156, 236,.9);  color: #fff;  height: 18px;  line-height:18px;  width:36px;  margin: 0 auto 5px;  font-size: 12px;
    text-align: center;  border-radius: 2px;  cursor: pointer;  position: absolute;  top: 28px;  left: 28px;  }
.imgsthumb:hover a,.imgsthumb:hover .canclePic{  display: block;  }
.imgWall{  display: -webkit-flex;  display: flex;align-items:center;flex-wrap: wrap;}
.imgsthumb .canclePic{  display: none;  }
#picShow{  position: fixed;  display: inline-flex;  justify-content: center;  border: 1px solid #aaa;  background: rgba(7, 7, 7, 0.64);  z-index: 100009;  width: 100%;
    height: 100%;  text-align: center;  vertical-align: middle;  top: 0;  left: 0;  align-items: center;  }
#picShow img{  margin-top:30px;  }
.imgScanWall{float: left;margin-left: 120px;}
.imgScanWall span{display: inline-block;  width: 80px;color: #036bc0;}
.agreenTip {  margin-right: 20px;  cursor: pointer;  }
.agreenTip i {  margin-right: 20px;  }
.agreenTip .fa-dot-circle-o {  color: #48cfad;  }
#signRecord .applyAll{margin-top: 30px;}
.gapLt{margin-left: 10px;}
.recordHeader span{margin-left: 8px;}




