package cn.sphd.miners.modules.inStockCheck.service.impl;

import cn.sphd.miners.modules.inStockCheck.entity.TMtInboundItemPackaging;
import cn.sphd.miners.modules.inStockCheck.mapper.TMtInboundItemPackagingMapper;
import cn.sphd.miners.modules.inStockCheck.service.TMtInboundItemPackagingService;
import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
@Service
public class TMtInboundItemPackagingServiceImpl implements TMtInboundItemPackagingService {

    @Autowired
    private TMtInboundItemPackagingMapper tMtInboundItemPackagingMapper;

    @Override
    public int deleteByPrimaryKey(Integer id) {
        return tMtInboundItemPackagingMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int deleteByPrimaryKeyIn(List<Integer> list) {
        return tMtInboundItemPackagingMapper.deleteByPrimaryKeyIn(list);
    }

    @Override
    public int insert(TMtInboundItemPackaging record) {
        return tMtInboundItemPackagingMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(TMtInboundItemPackaging record) {
        return tMtInboundItemPackagingMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(TMtInboundItemPackaging record) {
        return tMtInboundItemPackagingMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(TMtInboundItemPackaging record) {
        return tMtInboundItemPackagingMapper.insertSelective(record);
    }

    @Override
    public TMtInboundItemPackaging selectByPrimaryKey(Integer id) {
        return tMtInboundItemPackagingMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(TMtInboundItemPackaging record) {
        return tMtInboundItemPackagingMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(TMtInboundItemPackaging record) {
        return tMtInboundItemPackagingMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<TMtInboundItemPackaging> list) {
        return tMtInboundItemPackagingMapper.updateBatch(list);
    }

    @Override
    public int updateBatchUseMultiQuery(List<TMtInboundItemPackaging> list) {
        return tMtInboundItemPackagingMapper.updateBatchUseMultiQuery(list);
    }

    @Override
    public int updateBatchSelective(List<TMtInboundItemPackaging> list) {
        return tMtInboundItemPackagingMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<TMtInboundItemPackaging> list) {
        return tMtInboundItemPackagingMapper.batchInsert(list);
    }

    @Override
    public int batchInsertSelectiveUseDefaultForNull(List<TMtInboundItemPackaging> list) {
        return tMtInboundItemPackagingMapper.batchInsertSelectiveUseDefaultForNull(list);
    }

	@Override
	public List<TMtInboundItemPackaging> findByAll(TMtInboundItemPackaging tMtInboundItemPackaging){
		 return tMtInboundItemPackagingMapper.findByAll(tMtInboundItemPackaging);
	}




}
