<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.UserTestMapper">
    <select id="getSingle" parameterType="userTest">
        SELECT * FROM user WHERE username=#{username} AND password=#{password}
    </select>
    <insert id="insert" parameterType="userTest" useGeneratedKeys="true">
        INSERT INTO user (id,username,password) VALUES (#{id},#{username},#{password})
    </insert>
    <update id="update" parameterType="userTest">
        UPDATE user SET username=#{username},password='333333' WHERE id=10
    </update>
    <delete id="delete" parameterType="int">
        DELETE FROM user WHERE id=#{id}
    </delete>
</mapper>