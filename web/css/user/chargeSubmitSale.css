
/*查看处理页面*/
.finance_hide,.dealSee,.dealSee_con{
    display: none;
}

.dealSee_head {
    padding: 10px 0 20px 5px;
    border-bottom: 1px solid #e4e4e4;
    font-size: 22px;
    font-weight: 400;
    color: #333;
}

.pic {
    margin: 30px 0;
    padding: 9px 30px 14px 20px;
    background: #fff url(../../assets/img/goback.png) no-repeat center center;
    background-size: 40px;
    cursor: pointer;
}

.dealSee_con {
    background-color: #f2f2f2;
    margin-top: 20px;
    padding: 20px;
}

.table_con {
    margin-top: 50px;
    font-size: 14px;
}

.t_left {
    text-align: right !important;
    font-weight: 700;
}
.t_right {
    text-align: left !important;
    font-weight: 400;
}

.resourceAddBtn {
    position: relative;
    top: -4px;
}
.azury1{
    color: #fff;
    background: #3598dc;
    text-decoration: none;
}

.aboutbtn1 {
    display: inline-block;
    cursor: pointer;
    margin-right: 5px;
    padding: 5px 25px;
    font-weight: 400;
}

.azury_reject, .azury_reject:hover {
    color: #fff;
    background: #e7505a;
    text-decoration: none;
}
.aboutbtn_reject {
    display: inline-block;
    cursor: pointer;
    margin-right: 5px;
    padding: 5px 15px;
    font-weight: 400;
}
.azury_reject:hover {
    color: #fff;
    background-color: #e12330;
}
.azury1:hover {
    color: #fff;
    background-color: #217ebd;
}

.dealSee_process > div:nth-child(1), .dealSee_processwipe > div:nth-child(1) {
    padding: 23px 0 8px 0;
}
.dealSee_Fon {
    color: #999;
}

.dealSee_person {
    display: inline-block;
    width: 230px;
}

.pic_yes {
    margin: 30px 0;
    padding: 14px 21px 8px 24px;
    background: #f2f2f2 url(../../assets/img/yes.png) no-repeat center center;
    background-size: 30px;
}
.dealSee_time {
    display: inline-block;
    width: 200px;
}

.dealSee_process > div:nth-child(2), .dealSee_process > div:nth-child(3), .dealSee_process > div:nth-child(4), .dealSee_process > div:nth-child(5), .dealSee_processwipe > div:nth-child(2), .dealSee_processwipe > div:nth-child(3), .dealSee_processwipe > div:nth-child(4), .dealSee_processwipe > div:nth-child(5) {
    padding: 8px 0;
}
.dealSee_process > div:last-child {
    color: #3598dc;
}
.pic_deal {
    margin: 30px 0;
    padding: 11px 21px 10px 23px;
    background: #f2f2f2 url(../../assets/img/deal.png) no-repeat center center;
    background-size: 35px;
}

.wipe_deal > span {
    font-size: 20px;
    font-weight: 700;
    display:block;
    margin-bottom:20px;
    color: #3598dc;
}


