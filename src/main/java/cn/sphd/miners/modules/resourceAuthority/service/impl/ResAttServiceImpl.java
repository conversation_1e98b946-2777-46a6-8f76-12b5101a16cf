package cn.sphd.miners.modules.resourceAuthority.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.resourceAuthority.dao.ResAttDao;
import cn.sphd.miners.modules.resourceAuthority.dao.ResAttHisDao;
import cn.sphd.miners.modules.resourceAuthority.entity.ResAtt;
import cn.sphd.miners.modules.resourceAuthority.entity.ResAttHis;
import cn.sphd.miners.modules.resourceAuthority.entity.ResCorrelation;
import cn.sphd.miners.modules.resourceAuthority.mapper.ResAclMapper;
import cn.sphd.miners.modules.resourceAuthority.service.ResAttService;
import cn.sphd.miners.modules.resourceAuthority.service.ResCorrelationService;
import cn.sphd.miners.modules.resourceAuthority.service.ResNoticeService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.uploads.service.UploadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by 朱思旭 on 2022/1/28.
 */
@Service
@Transactional(readOnly=false)
public class ResAttServiceImpl implements ResAttService {

    @Autowired
    ResAclMapper resAclMapper;

    @Autowired
    ResAttDao resAttDao;
    @Autowired
    ResAttHisDao resAttHisDao;



    @Autowired
    ResCorrelationService resCorrelationService;
    @Autowired
    UploadService uploadService;
    @Autowired
    ResNoticeService resNoticeService;


    @Override
    public HashMap<String, Object> insertForm(User user, String sn, String name, String type, String path, String size, String module, Integer resource) {
        Integer state = this.checkFormSn(user.getOid(),sn);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        if (state.equals(1)) {
            ResAtt r = null;
            if (resource.equals(0)) {
                r = this.addResAtt(user,sn,name,type,path,size,"0","0",module);
            } else {
                r = this.addResAtt(user,sn,name,type,path,size,"1","0",module);
                ResCorrelation resCorrelation = resCorrelationService.addCorrliation(user,resource,r.getId(),"4", r.getCreateDate());
                List<Integer> listUserids =  resAclMapper.getAllUserIdsByFileId(resource);
                resNoticeService.addSpecialAuthUserId(listUserids, user);
                resNoticeService.sendResFileNotice(user, resource, r, resCorrelation.getId(), null, null, null, null, "10", listUserids, null, null);
            }
            ResAttHis rh = this.addResAttHis(user,r.getId(),sn,name,type,path,size,r.getState(),r.getVersion(),r.getEnabled(),"1",r.getCreateDate(),module);
            map.put("ResAtt", r);
        }
        return map;
    }

    @Override
    public HashMap<String, Object> getResAttByType(User user, String type, PageInfo pageInfo, String name) {
        String hql = "from ResAtt where org = :org and state = :state";
        HashMap<String,Object> param = new HashMap<>();
        param.put("org",user.getOid());
        if ("1".equals(type)) {
            param.put("state","0");
        } else {
            hql = hql + " and enabled = :enabled";
            param.put("state","1");
            if ("2".equals(type)){
                param.put("enabled",true);
            }else {
                param.put("enabled",false);
            }
        }
        if ( name != null) {
            hql = hql + "  and ( name like :name or sn like :sn )" ;
            param.put("name", "%"+name+"%");
            param.put("sn", "%"+name+"%");
        }
        List<ResAtt> list = resAttDao.getListByHQLWithNamedParams(hql,param,pageInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", list);
        map.put("pageInfo", pageInfo);
        return map;
    }

    @Override
    public Integer delResAtt(User user, Integer id) {
        ResAtt resAtt = this.getResAttSingle(id);
        Integer state = 1;
        if ("0".equals(resAtt.getState())){
            state = resCorrelationService.checkCorrelation(id,null);
            if (state == 1) {
                ResAttUsing  callback = new ResAttUsing(resAtt.getId(),resAtt.getClass());
                uploadService.delFileUsing(callback, resAtt.getPath(), user);
                String hql = " from ResAttHis where attachment = :attachment and path is not null";
                HashMap<String,Object> param = new HashMap<>();
                param.put("attachment",id);
                List<ResAttHis> list = resAttHisDao.getListByHQLWithNamedParams(hql,param);
                if (!list.isEmpty()) {
                    for (ResAttHis rh : list) {
                        String oldFilename = rh.getPath();
                        ResAttUsing  callbackHis = new ResAttUsing(rh.getId(),rh.getClass());
                        uploadService.delFileUsing(callbackHis, oldFilename, user);
                    }
                }
                String delAttHisHql = "delete from ResAttHis where attachment = :attachment";
                HashMap<String, Object> paramdelAttHis = new HashMap<>();
                paramdelAttHis.put("attachment", id);
                Integer stateDelAttHis = resAttHisDao.queryHQLWithNamedParams(delAttHisHql, paramdelAttHis);
                String delAttHql = "delete from ResAtt where id = :id";
                HashMap<String, Object> paramdelAtt = new HashMap<>();
                paramdelAtt.put("id", id);
                Integer stateDelAtt = resAttDao.queryHQLWithNamedParams(delAttHql, paramdelAtt);
            }
        }else {
            state = 2;
        }
        return state;
    }

    @Override
    public HashMap<String, Object> updateResAtt(User user, Integer id, String sn, String name) {
        Integer state = 1;
        if (sn != null) {
            state = this.checkFormSn(user.getOid(),sn);
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        if (state == 1) {
            ResAtt resAtt = this.getResAttSingle(id);
            String oldName = resAtt.getSn() + "/" + resAtt.getName();
            if (sn != null) {
                resAtt.setSn(sn);
            }
            if (name != null) {
                resAtt.setName(name);
            }
            String newName = resAtt.getSn() + "/" + resAtt.getName();
            ResAttHis resAttHis = this.addResAttHis(user,id,resAtt.getSn(),resAtt.getName(),null,null,null,resAtt.getState(),resAtt.getVersion(),resAtt.getEnabled(),"2",new Date(),null);
            map.put("resAtt", resAtt);
            List<ResCorrelation> listAtt = resCorrelationService.getCorrFile(id);
            if (!listAtt.isEmpty()) {
                List<Integer> listAllUser = new ArrayList<>();
                for (ResCorrelation r : listAtt) {
                    List<Integer> listUser =  resAclMapper.getAllUserIdsByFileId(r.getResource());
                    listAllUser.addAll(listUser);
                }
                listAllUser = listAllUser.stream().distinct().collect(Collectors.toList());
                resNoticeService.addSpecialAuthUserId(listAllUser, user);  //填入特殊权限
                resNoticeService.sendResFileNotice(user,null,null,null,null,null,null,null,"16",listAllUser,oldName,newName);
            }
        }
        return map;
    }

    @Override
    public ResAtt changeResAttVersion(User user, Integer id, String type, String path, String size, String module) {
        ResAtt resAtt = this.getResAttSingle(id);
        String oldPath = resAtt.getPath();
        ResAttUsing callBack = new ResAttUsing(resAtt.getId(),resAtt.getClass());
        uploadService.delFileUsing(callBack,oldPath,user);
        resAtt.setType(type);
        resAtt.setPath(path);
        resAtt.setSize(Long.valueOf(size));
        Integer versionOn = Integer.valueOf(resAtt.getVersion())+1;
        resAtt.setVersion(versionOn.toString());
        resAtt.setCreator(user.getUserID());
        resAtt.setCreateName(user.getUserName());
        resAtt.setCreateDate(new Date());
        uploadService.addFileUsing(callBack,path,resAtt.getName(),user,module);
        ResAttHis resAttHis = this.addResAttHis(user,resAtt.getId(),resAtt.getSn(),resAtt.getName(),resAtt.getType(),resAtt.getPath(),size,resAtt.getState(),resAtt.getVersion(),resAtt.getEnabled(),"1",resAtt.getCreateDate(),module);
        return resAtt;
    }

    @Override
    public HashMap<String, Object> getResAttHisRecord(Integer id, PageInfo pageInfo) {
        String hql = " from ResAttHis where attachment = :attachment";
        HashMap<String,Object> param = new HashMap<>();
        param.put("attachment",id);
        List<ResAttHis> list = resAttHisDao.getListByHQLWithNamedParams(hql,param,pageInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", list);
        map.put("pageInfo", pageInfo);
        return map;
    }

    @Override
    public ResAtt getResAttSingle(Integer id) {
        ResAtt resAtt = resAttDao.get(id);
        return resAtt;
    }

    @Override
    public ResAttHis getResAttHisSingle(Integer id) {
        ResAttHis resAttHis = resAttHisDao.get(id);
        return resAttHis;
    }


    private Integer checkFormSn(Integer org, String sn){
        String hql = "select count(id) from ResAtt where sn = :sn and org = :org";
        HashMap<String,Object> param = new HashMap<>();
        param.put("sn",sn);
        param.put("org",org);
        Long num = (Long) resAttDao.getByHQLWithNamedParams(hql,param);
        Integer state = 1;
        if (num > 0) {
            state = 2;
        }
        return state;
    }

    //新增附件表数据
    private ResAtt addResAtt(User user, String sn, String name, String type, String path, String size, String state, String version, String module){
        ResAtt r = new ResAtt();
        r.setSn(sn);
        r.setName(name);
        r.setType(type);
        r.setPath(path);
        r.setSize(Long.valueOf(size));
        r.setState(state);
        r.setVersion(version);
        r.setEnabled(true);
        r.setOrg(user.getOid());
        r.setCreator(user.getUserID());
        r.setCreateName(user.getUserName());
        r.setCreateDate(new Date());
        resAttDao.save(r);
        ResAttUsing callback = new ResAttUsing(r.getId(),r.getClass());
        uploadService.addFileUsing(callback,r.getPath(),r.getName(),user, module);
        return r;
    }

    //新增附件历史表数据
    private ResAttHis addResAttHis(User user, Integer attId, String sn, String name, String type, String path, String size, String state, String version, Boolean enabled, String operation, Date createDate, String module){
        ResAttHis rh = new ResAttHis();
        rh.setAttachment(attId);
        rh.setSn(sn);
        rh.setName(name);
        rh.setType(type);
        rh.setPath(path);
        if(size != null){
            rh.setSize(Long.valueOf(size));
        }
        rh.setState(state);
        rh.setVersion(version);
        rh.setEnabled(enabled);
        rh.setOperation(operation);
        rh.setOrg(user.getOid());
        rh.setCreator(user.getUserID());
        rh.setCreateName(user.getUserName());
        rh.setCreateDate(createDate);
        resAttHisDao.save(rh);
        if (rh.getPath() != null) {
            ResAttUsing callback = new ResAttUsing(rh.getId(),rh.getClass());
            uploadService.addFileUsing(callback,rh.getPath(),rh.getName(),user, module);
        }
        return rh;
    }

}
