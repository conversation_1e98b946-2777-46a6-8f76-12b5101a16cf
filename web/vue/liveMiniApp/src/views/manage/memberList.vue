<template>
	<!-- 团队成员管理 -->
	<div class="">
		<Pageheader title="团队成员管理" :showBack="true"/>
    <div class="live-panel live-tr litF" @click="litShare">
      <span>分享给自己的好友或粉丝，招纳其成为团队成员</span>
      <el-icon class="live-right" >
        <arrow-right-bold color="#53B5A8" class="houseSty8" />
      </el-icon>
    </div>
    <div class="live-panel">
      <div class="litF live-tr">
        您本人以外的团队成员现共{{ list.length || 0 }}名
        <span class="live-right line-link" @click="goPage('memberOutList')">
          已移出团队的成员
          <el-icon class="relaRight"><arrow-right-bold color="#53B5A8" class="houseSty8" /></el-icon>
        </span>
      </div>
      <div class="officeList">
        <component :is="LiveTable" :columns="columns" :list="list" @longPress="longPress"></component>
      </div>
    </div>
    <BottomDialog v-show="isShown">
        <div class="live-actionSheet" @click="cancelTeam">
          移出团队
        </div>
        <div class="live-actionSheet" @click="hideDialog">
          取 消
        </div>
    </BottomDialog>
	</div>
</template>
<script setup>
import LiveTable from '../../components/liveTable.vue'
import BottomDialog from '../../components/BottomDialog.vue'
import { getTeamMembers, ttAassistantSharing, memberOutTeam } from "@/api/api.js";
import { ref, onMounted, reactive } from "vue";
import { ElMessage } from 'element-plus'
import { useRouter,useRoute  } from 'vue-router';
import JSONBig from 'json-bigint'
import auth from '@/sys/auth'

const router = useRouter();  //用router.options.routes  渲染菜单
let list = reactive([])
let columns = [
  {
    title: "抖音名",
    key: "name",
    flex:1
  },
  {
    title: '加入时间',
    key: 'time',
    flex:2
  },
  {
    title: '手机号(账号)',
    key: 'mobile',
    flex:1
  }
]
const isShown = ref(false)
let editUser = {}
const shareParams = ref('')

onMounted(()=>{
  getMembers()
  getShareData()
})

  const longPress = (item, index) => {
    console.log('index =', index);
    console.log(item)
    editUser = item
    isShown.value = true

  }

  const hideDialog = function(){
    isShown.value = false

  }

  const goPage = function (url){
    router.push({ name: url })
  }
  const cancelTeam = function (){
  console.log('editUser', editUser)
    // 移除团队
    memberOutTeam({ outUserId: editUser.id }).then((res)=>{
      console.log('memberOutTeam res:', res)
      isShown.value = false
      if(res){
        ElMessage.success({
          message: '操作成功',
          type: 'success',
        })
        list.splice(editUser.index,1);

      }else{
        ElMessage.error('操作失败')
      }
    })

  }
  const getMembers = function(){
    getTeamMembers().then(res => {
      console.log('getTeamMembers response', res )
      let members = res.data;
      if(members.length > 0){
        members.forEach(item => {
          if(item.submitState == 0){
            item.mobile = '已设为不展示'
          }
          list.push({
            name: item.userName,
            time: "2020-02-02 12:12:12",
            mobile: item.mobile,
            id: item.userID,
          })
        });
      }

    }).catch(res =>{
      console.log('txt error', res)
    });
  }
  const litShare = () => {
    console.log('要跳转 litShare 了 shareTtAppMes',  shareParams.value)
    tt.miniProgram.navigateTo({
      url: `/pages/start/start?${ shareParams.value}`, //demo示例，一般路径形式：/pages/detail/detail?key=${value}
      success(res) {
        console.log('跳转到 小程序的分享页');
        console.log(res);
      },
      fail(res) {
        console.log('跳转到 小程序的分享页 失败');
        console.log("navigateTo调用失败");
      },
    });
  }
  const getShareData = () => {
    const userInfoSys = auth.getUser()
    let sendDate = {
      subOrg:userInfoSys.oid,
      accId:userInfoSys.accId,
      type:2,
      module:"assistant",
    }
    ttAassistantSharing(sendDate).then(res => {
      let ddata = res.data.shareTtAppMes
      console.log('ttAassistantSharing response', ddata)
      shareParams.value = `shareTime=${ ddata.shareTime }&shareName=${ ddata.shareName }&type=2&oid=${ userInfoSys.oid }`

    }).catch(res =>{
      console.log('ttAassistantSharing error', res)
    });
  }

</script>
<style lang="scss" scoped>
.litF{ font-size: 0.9em; }
.houseSty8{
  position: relative;
  top:7px;
}
</style>
