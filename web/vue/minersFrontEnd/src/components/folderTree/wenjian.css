/* ----------------------upload--------------------*/
.Upload{
    width: 100%;
    height: 64px;
    border:1px dashed #ccc;
    margin-bottom:8px;
    border-radius: 5px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    padding:10px;
}
.Upload p{
    color: #666;
}
.col-hr{
    float: left;
    border-left:1px dashed #ccc;
    height: 390px;
    margin-left: 23px;
}
.uploadify-button,.uploadDeleteBtn {
    border: none;
    background-color: #e7e7e7;
    line-height: 12px;
    border-radius: 3px;
    padding: 3px 6px;
    font-size: 12px;
    font-weight: 400;
    color: #535353;
    cursor: pointer;
    text-decoration: none;

}
.uploadify-button:hover{
    background-color: #5d9cec;
    color: #fff;
}
.uploadDeleteBtn:hover{
    background-color: #ed5565;
    color: #fff;
}
.tips{
    line-height: 40px;
}
.fileType{
    width: 30px;
    height: 40px;
    margin-right: 10px;
}
.up_filename{
    width:200px;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
}
/* -------------------end--------------------*/
.ty-fileNull{
    height: 128px;
    padding-top: 120px;
    text-align: center;
    background-color: #fff;
    min-height: 593px;
}
.ty-fileNull img{
    width: 128px;
}
.ty-fileNull p{
    color: #aaa;
    font-size: 16px;
}
.seeDetail{
    display: block;
    font-size: 12px;
    padding: 2px 10px;
    border-radius: 3px;
    font-weight: bold;
    cursor: pointer;
    text-align: center;
}
.seeDetail:hover{
    color: #fff;
    background-color: #5d9cec;
}

/* --------------------more--------------------*/
ul.hel{
    position: absolute;
    width: 84px;
    right: 0;
    top: 25px;
    padding: 2px 0;
    background-color: #fff;
    box-shadow: 0 0 3px #aaa;
    z-index: 2;
}
.ulli{
    float: left;
    width: 100%;
}
.ulli span{
    display: block;
    height: 28px;
    line-height: 28px;
    text-align:left;
    margin:0 2px;
    padding: 0 4px;
    border-radius: 2px;
    transition: all .2s;
}
.ulli:not(.ty-disabled) span{
    color: #5d9cec;
}
.ulli:not(.ty-disabled) span:hover{
    background-color: #5d9cec;
    color: #fff;
}

/* --------------------end--------------------*/
.ty-fileItem > .ty-fileHandle >.handleActive{
    background-color: #5d9cec;
    color:#fff;
}
.ty-fileHandle{
    position: absolute;
    right: 10px;
}

.ty-searchContent{
    padding-top: 8px;
}
.employeeQuery .searchCon{
    padding: 20px;
}
.employeeQuery input{
    border:1px solid #dee8f0;
    background-color: #fff;
    line-height: 36px;
    width: 100%;
    text-align: left;
    padding: 0 8px;
    color: #3f3f3f;
    height: 36px;
}
.employeeQuery input:focus{
    border:1px solid #5d9cec;
}
.eq_item{
    margin-bottom: 10px;
    padding-bottom: 10px;
}
.eq_item .eq_l{
    float: left;
    width: 20%;
    line-height:2em;
    text-align: right;
    padding-right:20px;
}
.eq_item .eq_r{
    float: left;
    width: 80%;
    line-height:2em;
    white-space: normal;
}
.searchCon{
    margin-top: 20px;
}
.currentFileName,.currentFileNo{
    display: block;
    font-size: 14px;
    color: #5d9cec;
    margin: 12px 0;
}
.changeFileName,.changeFileNo{
    margin: 12px 0;
}
.ty-disabled,.ty-disabled span{
    color: #aaa;
    cursor: default;
}
.ty-disabled:hover,.ty-disabled span:hover{
    cursor: default;
}

/* 文件查看基本信息 */
.infoCon>div{
    width:385px;
}
.trItem{
    padding:5px 8px;
}
.trItem .ttl, .trItem .ttl2, .trItem .ttl3{
    display: inline-block;
    color: #6e7c8a;
    vertical-align: top;
}
.trItem .ttl{
    width:80px;
    text-align: right;
}
.trItem .ttl2{
    width:70px;
    text-align: right;
}
.trItem .ttl3{
    width:150px;
    text-align: left;
}
.trItem .con{
    display: inline-block;
    word-wrap: break-word;
    word-spacing:normal;
    max-width: 280px;
}
.currentFileName,.currentFileNo{
    word-wrap: break-word;
    word-break:break-all;
}
#docInfoScan{
    width: 690px;
}
#changeDoc , .notZW {
    display: none;
}
.ty-colFileTree .fa-folder+span{
    width:100px;
    display:inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.mar{
    width: 1000px;
    flex-grow: 0;
    flex-shrink: 0;
    margin-left: 8px;
}
.ty-fileList{
    margin-top: 8px;
}

.changeHistory{
    border-top: 1px solid #eee;
    margin-top: 8px;
}
.changeHistory button{
    margin-left: 10px;
}
.bounce-uploadFile .ty-right,.bounce-CVuploadFile .ty-right{
    width:48%;
}
.bounce .fa{
    color: #48cfad;
    margin-right: 5px;
    font-size: 14px;
    width:16px;
}
.directSect{
    margin-top:30px;
    width:100%;
}
.clearName{
    position:relative;
}
.clearNameFa{
    position:absolute;
    top:9px;
    right:0;
}
#uploadDirect,#changeDirect{
    font-style:normal;
}
.fileDirectSect,.changeDirectSect{
    margin-top:28px;
}
.ty-secondTab span.sort{
    font-size: 12px;
    font-family: 宋体;
    padding: 2px 8px 2px;
    border-radius: 1px;
    font-weight: bold;
    cursor: pointer;
    color: #fff;
    margin-left: 5px;
    background-color:#5d9cec;
}
.item-header{
    font-size: 14px;
    font-weight: 700;
    padding-left: 5px;
    border-left: 3px solid #ccc;
    margin: 16px 0;
    line-height: 1;
    color: #606266;
}
.info_title{
    font-weight: bold;
    color: #576471;
    font-size: 16px;
    margin-top: 10px;
    margin-bottom: 10px;
}
.censusInfo .times{
    width: 60px;
}
.info_name{
    display: inline-block;
    width: 64px;
    overflow: hidden;
    vertical-align: top;
    height: 20px;
}
.setBgHeight{min-height:613px;}


/*新的*/
.upload_avatar{
    margin-bottom: 8px;
}
.item-row, .item-column{
    display: flex;
    flex-direction: row;
    margin-bottom: 8px;
    padding: 0 8px;
}
.item-column{
    flex-direction: column;
}
.item-title{
    width: 80px;
    line-height: 30px;
}
.item-title-long{
     width: 280px;
 }
.item-content{
    flex-grow: 1;
    position: relative;
}
.item-content .clearInput{
    position: absolute;
    top: 1px;
    right: 0;
    color: #ccc;
    line-height: 30px;
    font-size: 16px;
}
.savePlace{
    color: #444;
    font-size: 13px;
    background-color: #f0f9eb;
    border-radius: 3px;
    line-height: 1.5;
    padding: 4px 8px;
    border: 1px solid #e6e6e6;
}
.savePlace i.fa {
    margin-left: 8px;
    color: #ddc667;
    vertical-align: middle;
}
.text_disabled{
    width: 100%;
    border: 1px solid #dcdfe6;
    background-color: #efefef;
    border-radius: 2px;
    display: inline-block;
    line-height: 1.5;
    box-sizing: border-box;
    color: #606266;
    font-size: inherit;
    outline: none;
    padding: 4px 8px;
}
#fileUpload input.ty-inputText, #folderUpload input.ty-inputText{
    width: 100%;
}
.ty-hr{
    border-bottom: 1px solid #0b9df9;
}
.hr{
    border-bottom: 1px solid #d8e4ee;
    margin: 8px 0;
}

/*文件夹上传限高*/
.k-upload-files{
    max-height:300px;
    overflow-y:scroll;
}
.ty-fileContent{
    display: flex;
    flex-direction: row;
    justify-content:flex-start;
}

#listDoc {
    max-width: 450px;
}
#scanSet { width:760px;  }
#scanSet .departTree > ul, #scanSet .departTree>form{ height:250px;  }
#scanSet  .departTree .arrow { line-height:270px  }
#scanSet .departTree{ width:720px;    }
#scanSet .ctrl{ float:right; line-height:30px; color:#0b94ea; cursor: default;  }
#scanSet .ctrl:hover{ text-decoration:underline;   }
.txtR{ text-align: right; }
.btnLink{ color:#0b94ea;cursor:default;     }
.btnLink:hover{ text-decoration:underline;   }
.ty-radio {position: relative;display: inline-block;margin-right: 12px;}
.ty-radio input {vertical-align: middle;margin-top: -2px;margin-bottom: 1px;width: 16px;height: 16px;appearance: none;-webkit-appearance: none;opacity: 0;outline: none;}
.clearfix{overflow: hidden;}
.ty-radio input+label {
    position: absolute;
    left: 0;
    top: calc(50% - 8px);;
    /*z-index: -1;*/
    /*注意层级关系，如果不把label层级设为最低，会遮挡住input而不能单选*/
    width: 14px;
    height: 14px;
    border: 1px solid #acacac;
    border-radius: 50%;
    cursor: pointer;
    transition: .2s;
}
.ty-radio input:disabled + label{
    border: 1px solid #d9d9d9;
    background: #f2f2f2;
    cursor: default;
}

.ty-radio input:checked+label {
    border: 1px solid #3d8fec;
}
.ty-radio input:checked:disabled+label {
    border: 1px solid #dcdfe6;
    background-color: #f5f7fa;
}
.ty-radio input:checked+label::after {
    content: "";
    position: absolute;
    left: 50%;
    top: 50%;
    width: 6px;
    height: 6px;
    background: #5d9cec;
    border-radius: 50%;
    transform: translate(-50%,-50%) scale(1);
    -webkit-transition: transform .15s ease-in;
    -moz-transition: transform .15s ease-in;
    -ms-transition: transform .15s ease-in;
    -o-transition: transform .15s ease-in;
    transition: transform .15s ease-in;
}
.ty-radio input:checked:disabled+label::after {
    background: #c0c4cc;
}
.ty-fileItem .ty-radio {
    height: 40px;
}



