<!--子路由 按时间查看-->
<template>
  <ul class="val_list">
    <li v-for="(item, index) in accountList" :key="index" class="ta-list">
      <div @click="goMonth(item)">
        <span>{{ item.name }}</span>
        <div class="current-balance-1">
          <div class="ty-color-green">
            <p>收入</p>
            <p>{{ item.income }}</p>
          </div>
        </div>
        <div class="current-balance-1 ty-color-red">
          <div>
            <p>支出</p>
            <p>{{ item.expend }}</p>
          </div>
        </div>
        <span>
        <el-icon><arrow-right /></el-icon>
      </span>
      </div>
    </li>
  </ul>
</template>

<script>

import finance from '@/mixins/finance.js'
export default {
  // name: "ViewByTime", 两个地方用这个页面了
  data() {
    return {
      accountList: []
    }
  },
  mixins: [finance],
  mounted() {
    let name = this.$route.name
    this.getTimeData(4)

  },
  methods: {
    getTimeData(state){
      let org = this.auth.getOrg()
      let params = { oid:org.id , state:state }
      this.financeApi.getAllAccountDataByTimeApp(params).then(res1=>{
        let res = res1.data.data
        let accountDetailList = res.accountDetailList
        let accountPeriodList = res.accountPeriodList
        this.accountList = []
        accountPeriodList.forEach(item=>{
          let iihk = { name: (new Date(item.beginDate).format('yyyy-MM')) ,
            income: this.formatCurrency(item.credit) , expend: this.formatCurrency(item.debit) }
          this.accountList.push(iihk)
        })
      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })
    },
    goMonth(item){
      if(item.name){
        localStorage.setItem('currentAccountMonth_beginDate', item.name )
        let path = `/currentAccountMonthDur/null`
        this.$router.push({ path:path })
      }else{
        this.$message.error('没有具体日期哦！')
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";

</style>