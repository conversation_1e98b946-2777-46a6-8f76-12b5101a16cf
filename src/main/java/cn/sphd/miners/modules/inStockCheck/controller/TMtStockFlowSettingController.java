package cn.sphd.miners.modules.inStockCheck.controller;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSetting;
import cn.sphd.miners.modules.inStockCheck.service.impl.TMtStockFlowSettingServiceImpl;
import cn.sphd.miners.modules.system.dao.ApprovalItemDao;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.web.bind.annotation.*;

import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Date;

/**
 * (t_mt_stock_flow_setting)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mfs")
public class TMtStockFlowSettingController {
  /** 服务对象 */
  @Autowired private TMtStockFlowSettingServiceImpl tMtStockFlowSettingServiceImpl;

  @Autowired
  ApprovalItemDao approvalItemDao;
  /**
   * 通过主键查询单条数据
   *
   * @param id 主键
   * @return 单条数据
   */
  @GetMapping("selectOne")
  public TMtStockFlowSetting selectOne(Integer id) {
    return tMtStockFlowSettingServiceImpl.selectByPrimaryKey(id);
  }

  @RequestMapping("updateSet")
  public int updateSet(TMtStockFlowSetting tMtStockFlowSetting, User user) {
    return tMtStockFlowSettingServiceImpl.updateByPrimaryKeySelective(tMtStockFlowSetting,user);
  }



  //临时使用，初始化出一条审批项
  @RequestMapping("/initItem")
  public String initItem(User user){
    ApprovalItem inStockMode = new ApprovalItem();
    inStockMode.setBelongTo(user.getOid());
    inStockMode.setLevel(1);
    inStockMode.setName("入库检查者设置");
    inStockMode.setDescription("入库检查者设置");
    inStockMode.setType("Update");
    inStockMode.setStatus(0);
    inStockMode.setOrders(230);
    inStockMode.setCode("inStockCheckerSet");
    inStockMode.setCreateName("系统");
    inStockMode.setCreateDate(new Date());
    inStockMode.setOpenDate(NewDateUtils.today(new Date()));
    inStockMode.setApproveStatus("2");
    inStockMode.setAuditDate(new Date());
    inStockMode.setUpperLimit(new BigDecimal(-1));
    approvalItemDao.save(inStockMode);

    return "success";
  }
}
