package cn.sphd.miners.modules.material.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.entity.SrmSupplierImage;
import cn.sphd.miners.modules.material.entity.SrmSupplierImageHistory;

import java.io.Serializable;

public interface SrmSupplierImageHistoryDao extends IBaseDao<SrmSupplierImageHistory, Serializable> {

    SrmSupplierImageHistory insert(SrmSupplierImage srmSupplierImage,Integer chId);
}
