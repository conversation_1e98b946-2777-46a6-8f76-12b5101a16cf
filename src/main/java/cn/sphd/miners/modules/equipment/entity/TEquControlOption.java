package cn.sphd.miners.modules.equipment.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.persistence.Transient;


/**
 * 装备管理_控制选项
20240407 1.292机器设备之初始设置 新增对象 t_equ_control_option
 * 
 * <AUTHOR>
 * @date 2024-04-13
 */
public class TEquControlOption
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 机构ID */

    private Long org;

    /** 名称 */

    private String name;

    /** 内容/场合 */

    private String content;

    /** 是否系统自带:1-系统类别,0-自定义类别 */

    private Integer isSystem;

    /** 是否可修改 */

    private Integer revisable;

    /** 状态:0-不启用,1-启用 */

    private Long enabled;

    /** 启停用时间 */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date enabledTime;

    /** 同级排序 */

    private Long orders;

    /** 描述 */

    private String memo;

    /** 创建人id */

    private Long creator;

    /** 创建人 */

    private String createName;

    /** 创建时间 */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;

    /** 修改人id */

    private Long updator;

    /** 修改人 */

    private String updateName;

    /** 修改时间 */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateDate;

    /** 操作:1-增,2-删,3-改,4-启用,5-停用 */

    private Long operation;

    /** 修改前记录ID */

    private Long previousId;

    /** 版本号,每次修改+1 */

    private Long versionNo;

    @Transient
    private String pics;



    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrg(Long org) 
    {
        this.org = org;
    }

    public Long getOrg() 
    {
        return org;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setIsSystem(Integer isSystem) 
    {
        this.isSystem = isSystem;
    }

    public Integer getIsSystem() 
    {
        return isSystem;
    }
    public void setRevisable(Integer revisable) 
    {
        this.revisable = revisable;
    }

    public Integer getRevisable() 
    {
        return revisable;
    }
    public void setEnabled(Long enabled) 
    {
        this.enabled = enabled;
    }

    public Long getEnabled() 
    {
        return enabled;
    }
    public void setEnabledTime(Date enabledTime) 
    {
        this.enabledTime = enabledTime;
    }

    public Date getEnabledTime()
    {
        return enabledTime;
    }
    public void setOrders(Long orders) 
    {
        this.orders = orders;
    }

    public Long getOrders() 
    {
        return orders;
    }
    public void setMemo(String memo) 
    {
        this.memo = memo;
    }

    public String getMemo() 
    {
        return memo;
    }
    public void setCreator(Long creator) 
    {
        this.creator = creator;
    }

    public Long getCreator() 
    {
        return creator;
    }
    public void setCreateName(String createName) 
    {
        this.createName = createName;
    }

    public String getCreateName() 
    {
        return createName;
    }
    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }
    public void setUpdator(Long updator) 
    {
        this.updator = updator;
    }

    public Long getUpdator() 
    {
        return updator;
    }
    public void setUpdateName(String updateName) 
    {
        this.updateName = updateName;
    }

    public String getUpdateName() 
    {
        return updateName;
    }
    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }
    public void setOperation(Long operation) 
    {
        this.operation = operation;
    }

    public Long getOperation() 
    {
        return operation;
    }
    public void setPreviousId(Long previousId) 
    {
        this.previousId = previousId;
    }

    public Long getPreviousId() 
    {
        return previousId;
    }
    public void setVersionNo(Long versionNo) 
    {
        this.versionNo = versionNo;
    }

    public Long getVersionNo() 
    {
        return versionNo;
    }

    public String getPics() {
        return pics;
    }

    public void setPics(String pics) {
        this.pics = pics;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("org", getOrg())
            .append("name", getName())
            .append("content", getContent())
            .append("isSystem", getIsSystem())
            .append("revisable", getRevisable())
            .append("enabled", getEnabled())
            .append("enabledTime", getEnabledTime())
            .append("orders", getOrders())
            .append("memo", getMemo())
            .append("creator", getCreator())
            .append("createName", getCreateName())
            .append("createDate", getCreateDate())
            .append("updator", getUpdator())
            .append("updateName", getUpdateName())
            .append("updateDate", getUpdateDate())
            .append("operation", getOperation())
            .append("previousId", getPreviousId())
            .append("versionNo", getVersionNo())
            .toString();
    }
}
