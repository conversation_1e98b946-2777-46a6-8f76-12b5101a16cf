
import {request} from "@/utils/request";
//机器设备模块
/*
*   creator: sy 2024-08-08  增加控制点
 */
export function addcontentd(data){
    data = data || {};
    return request({
        url:"../equ/control/point/add",
        method: 'post',
        data: data
    })
}

/*
*  creator: sy 2024-04-29   新增计量单位
 */
export function addUnatOk(data){
    data = data || {};
    return request({
        url:"/unit/addUnit.do",
        data: data
    })
}

/*
*   creator: sy 2024-09-02  点击确定按钮传值
 */
export function chankemadse(data){
    data = data || {};
    return request({
        url:"/equ/control/point/pointSet",
        method: 'post',
        data: data
    })
}

/*
*   creator: sy 2024-08-12  删除控制点
 */
export function dettentd(data){
    data = data || {};
    return request({
        url:"/equ/control/point/remove",
        method: 'post',
        data: data
    })
}

/*
*  creator: sy 2024-04-29   获取计量单位列表数据
 */
export function getUnitlank(data){
    data = data || {};
    return request({
        url:"/unit/selectUnitListForModule.do",
        data: data
    })
}

/*
*   creator: sy 2024-05-31  点击‘查看’按钮获取‘系统赋予的编码’相关数据/点击’装备器具明显‘下面的按钮获取’系统赋予的编码‘相关数据
 */
export function havelookern(data){
    data  = data || {};
    return request({
        url:"/equipment/model/list",
        method: "post",
        data: data
    })
}

/*
* creator: sy 2024-04-22    获取已设置的控制点数据
* */
export function initActtionarn(data){
    data = data || {}
    return request({
        url:"/equ/control/point/listWithMc",
        data: data
    })
}

/*
*   creator: sy 2024-07-10  获取装备数据
 */
export function initCractern2(data){
    data = data || {};
    return request({
        url:'/equipment/model/pageList',
        method:"post",
        data: data
    })
}

/*
*   creator: sy 2024-08-05  批量启用
 */
export function stopChcent(data){
    data = data || {};
    return request({
        url:"/equ/control/option/enable",
        method:'post',
        data: data
    })
}

/*
*   creator: sy 2024-08-01  进行选项的停用/启用
 */
export function stopxxest(data){
    data = data || {};
    return request({
        url:"/equ/control/option/edit",
        method: 'post',
        data: data
    })
}

/*
*   creator: sy 2024-08-14  修改控制点
 */
export function upcated(data){
    data = data || {};
    return request({
        url:"/equ/control/point/edit",
        method: 'post',
        data: data
    })
}

/*
* creator: sy 2024-04-28    点击选项管理获取列表数据
 */
export function initStatton(data){
    data = data || {}
    return request({
        url:"/equ/control/option/list",
        data: data
    })
}

/*
* creator: sy 2024-04-28    要新增的选项
 */
export function addname(data){
    data = data || {}
    return request({
        url:"/equ/control/option/add",
        data: data
    })
}