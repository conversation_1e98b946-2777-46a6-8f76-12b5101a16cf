<!--修改详情-->
<template>
  <div>
    <div class="head">
      <div style="width: 100%">
        <PageHeader :title="title" :showBack="true" :showClose="true" @mycloseFun="closeFun"></PageHeader>
      </div>
    </div>
    <p class="ttlSalary" v-if="info.type === 1">工资所属月份：{{ periodStr }}</p>
    <p class="ttlSalary" v-else>{{ periodStr }}{{ formatWageType(info.type) }}明细表</p>

    <div class="employeeBox" v-if="info.type === 1">
      <ul class="xiangUlB">
        <li></li>
        <li>
          <span>发放日期</span>
          <span v-html="info.payTime"></span>
        </li>
        <li>
          <span>发放人数</span>
          <span v-html="info.userNum "></span>
        </li>
        <li>
          <span>发放方式</span>
          <span v-html="info.payWay"></span>
        </li>
        <li>
          <span>本次发放总额</span>
          <span v-html="info.factPay"></span>
        </li>
        <li>
          <div style="display: flex;justify-content: space-between;width: 100%">
            <span style="width: 20%">创建</span>
            <div style="width: 80%; text-align: right;">
              <span>{{ info.createName }}</span> &nbsp;&nbsp;
              <span style="width: 220px">{{ info.createDate }}</span>
            </div>
          </div>
        </li>
        <li style="margin-bottom:20px; " v-if="subVersionNo">
          <div style="display: flex;justify-content: space-between;width: 100%">
            <span style="width: 20%">修改</span>
            <div style="width: 80%; text-align: right;">
              <span>{{ info.updateName }}</span> &nbsp;&nbsp;
              <span style="width: 220px">{{ info.updateDate }}</span>
            </div>
          </div>
        </li>
      </ul>
    </div>

    <div class="employeeBox" v-else>
      <ul class="xiangUlB">
        <li></li>
        <li>
          <span>支出日期</span>
          <span v-html="info.payTime"></span>
        </li>
        <li>
          <span>职工人数</span>
          <span v-html="info.userNum "></span>
        </li>
        <li>
          <span>支出方式</span>
          <span v-html="info.payWay"></span>
        </li>
        <li>
          <span>支出总额</span>
          <span v-html="info.factPay"></span>
        </li>
        <li>
          <div style="display: flex;justify-content: space-between;width: 100%">
            <span style="width: 20%">创建</span>
            <div style="width: 80%; text-align: right;">
              <span>{{ info.createName }}</span> &nbsp;&nbsp;
              <span style="width: 220px">{{ info.createDate }}</span>
            </div>
          </div>
        </li>
        <li style="margin-bottom:20px; " v-if="subVersionNo">
          <div style="display: flex;justify-content: space-between;width: 100%">
            <span style="width: 20%">修改</span>
            <div style="width: 80%; text-align: right;">
              <span>{{ info.updateName }}</span> &nbsp;&nbsp;
              <span style="width: 220px">{{ info.updateDate }}</span>
            </div>
          </div>
        </li>
      </ul>
    </div>

    <div class="hr_gray "></div>
    <p style="line-height: 40px; font-size: 16px;padding:0px 15px">※ 本次修改后的数据被标为了红色。</p>
    <div class="emTableBox">
      <table class="emTable">
        <thead>
        <th width="40%">姓名</th>
        <th width="30%">部门/岗位</th>
        <th width="30%">{{ info.type === 1 ? '实发金额' : '金额' }}</th>
        </thead>
        <tbody>
        <tr v-for="(item, index) in info.mapList" :key="index">
          <td v-html="item.userStr"></td>
          <td v-html="item.departPost"></td>
          <td v-html="item.factPay"></td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "changeLogRaw",
  data() {
    return {
      title:'',
      info: {},
      periodStr: '',
    };
  },
  mixins: [finance],
  created() {
    let changeLogRaw = localStorage.getItem('changeLogRaw')
    if(changeLogRaw){
      let info = JSON.parse(changeLogRaw)
      console.log('info=', info)
      this.periodStr = info.period.substr(0,4) + '年' + info.period.substr(4,2) + '月'
      let params = {
        period : info.period,
        subVersionNo : info.subVersionNo ,
        type : info.type ,
        versionNo : info.versionNo
      }
      console.log('params=', params)
      this.subVersionNo = params.subVersionNo
      this.getRecordDetailsFun(params)
    }else{
      this.$message.error('获取数据失败！')
      this.$router.go(-1)
    }
  },
  methods: {
    getRecordDetailsFun(params){
      this.financeApi.getRecordDetails(params).then(res1=>{
        let res = res1.data.data
        let newInfo = res.personnelPayHistories
        let oldInfo = res.personnelPayHistoriesOld
        console.log('newInfo=', newInfo)
        console.log('oldInfo=', oldInfo)

        this.info.type = newInfo.type
        this.title = this.formatWageType(this.info.type )
        if(params.subVersionNo === 0){ // 首次
          this.info.payTime = new Date(newInfo.payTime).format('yyyy-MM-dd')
          this.info.userNum = newInfo.userNum + '人'
          this.info.payWay = (newInfo.payWay === 1 ? '现金': '转账')
          this.info.factPay = this.formatCurrency(newInfo.pay)
          this.info.createName = newInfo.createName0
          this.info.createDate = new Date(newInfo.createDate0).format('yyyy-MM-dd hh:mm:ss')
          this.info.updateDate = new Date(newInfo.createDate).format('yyyy-MM-dd hh:mm:ss')
          this.info.updateName = newInfo.createName
          newInfo.mapList.forEach(userI=>{
            userI.userStr = `${ userI.userName } ${ userI.mobile  }`
            userI.departPost = `${ userI.departName || '--' }/${ userI.postName || '--'  }`
          })
          this.info.mapList = newInfo.mapList
        }
        else{ // 不是首次
          this.info.payTime = this.compareTxt( (new Date(oldInfo.payTime).format('yyyy-MM-dd')), (new Date(newInfo.payTime).format('yyyy-MM-dd')))
          this.info.userNum = this.compareTxt( (oldInfo.userNum + '人') , (newInfo.userNum + '人'))
          this.info.payWay = this.compareTxt( (oldInfo.payWay == 1 ? '现金' : '转账' ),(newInfo.payWay == 1 ? '现金' : '转账' ))
          this.info.factPay = this.compareTxt( this.formatCurrency(oldInfo.pay), this.formatCurrency(newInfo.pay))
          this.info.createName = newInfo.createName0
          this.info.createDate = new Date(newInfo.createDate0).format('yyyy-MM-dd hh:mm:ss')
          this.info.updateDate = new Date(newInfo.createDate).format('yyyy-MM-dd hh:mm:ss')
          this.info.updateName = newInfo.createName
          newInfo.mapList.forEach(userI=>{
            let isPiPei = false
            oldInfo.mapList.forEach(userIold=>{
              if(userIold.userId === userI.userId){
                isPiPei = true
                userI.factPay = this.compareTxt(userIold.factPay, userI.factPay)
              }
            })
            if(!isPiPei){
              userI.factPay = this.compareTxt('', userI.factPay)
              userI.userStr = this.compareTxt('', `${ userI.userName } ${ userI.mobile  }`)
              userI.departPost = this.compareTxt('', `${ userI.departName || '--' }/${ userI.postName || '--'  }`)
            }else{
              userI.userStr = `${ userI.userName } ${ userI.mobile  }`
              userI.departPost = `${ userI.departName || '--' }/${ userI.postName || '--'  }`
            }
          })
          this.info.mapList = newInfo.mapList

        }


      }).catch(err=>{
        this.$message.error('获取数据失败！')
        // this.$router.go(-1)
        console.log('err=', err)
      })

    }

  }
}
</script>

<style scoped lang="scss">
@import "src/style/account.scss";
@import "src/style/common.scss";

.xiangUlB {
  li:first-child {
    justify-content: center;

    p {
      font-size: 18px;
    }
  }

  li {
    span {
      color: #555555;
    }

  }

  li:last-child {
    justify-content: space-between;
    line-height: 35px;

  }
}
</style>