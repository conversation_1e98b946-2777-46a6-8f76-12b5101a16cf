package cn.sphd.miners.modules.project.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.project.dao.ProjectAttachmentDao;
import cn.sphd.miners.modules.project.dao.ProjectBaseDao;
import cn.sphd.miners.modules.project.entity.ProjectBase;
import cn.sphd.miners.modules.project.service.ProjectBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by Administrator on 2018/5/31.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class ProjectBaseServiceImpl implements ProjectBaseService {

    @Autowired
    ProjectBaseDao projectBaseDao;
    @Autowired
    ProjectAttachmentDao projectAttachmentDao;

    @Override
    public List<ProjectBase> getProjectBaseByState(Integer oid, String state,Integer initialReceipter, PageInfo pageInfo) {
        String hql="from ProjectBase o where o.org=:org and o.state=:state";
        HashMap<String,Object> map = new HashMap<String, Object>();
        map.put("org",oid);
        map.put("state",state);
        if (initialReceipter!=null){
            hql+=" and o.initialReceipter=:initialReceipter";
            map.put("initialReceipter",initialReceipter);
        }
        hql+=" order by id desc";
        return projectBaseDao.getListByHQLWithNamedParams(hql,map,pageInfo);
    }

    @Override
    public void addProjectBase(ProjectBase projectBase) {
        projectBaseDao.save(projectBase);
    }

    @Override
    public void updateProjectBase(ProjectBase projectBase) {
        projectBase.setUpdateDate(new Date());
        projectBaseDao.update(projectBase);
    }

    @Override
    public ProjectBase getProjectBaseById(Integer id) {
        return projectBaseDao.get(id);
    }

    @Override
    public List<ProjectBase> getProjectBaseByDate(Integer oid, String state, Integer initialReceipter, Date beginDate, Date endDate, PageInfo pageInfo) {
        String hql="from ProjectBase o where o.org=:org and o.state=:state and o.updateDate>=:beginDate and o.updateDate<=:endDate";
        HashMap<String,Object> map = new HashMap<String, Object>();
        map.put("org",oid);
        map.put("state",state);
        map.put("beginDate",beginDate);
        map.put("endDate",endDate);
        if (initialReceipter!=null){
            hql+=" and o.initialReceipter=:initialReceipter";
            map.put("initialReceipter",initialReceipter);
        }
        hql+=" order by o.settleTimeFact desc";
        return projectBaseDao.getListByHQLWithNamedParams(hql,map,pageInfo);
    }
}
