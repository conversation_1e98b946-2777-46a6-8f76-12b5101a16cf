package cn.sphd.miners.modules.commodity.service.impl;

import cn.sphd.miners.modules.commodity.dao.*;
import cn.sphd.miners.modules.commodity.entity.*;
import cn.sphd.miners.modules.commodity.service.ProductImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("productImageService")
public class ProductImageServiceImpl implements ProductImageService {

    @Autowired
    private PdCommodityMediaDao pdCommodityMediaDao;
    @Autowired
    private PdCommodityMediaHistoryDao pdCommodityMediaHistoryDao;
    @Autowired
    private PdMerchandiseDao pdMerchandiseDao;

    @Autowired
    private PdImageDao pdImageDao;
    @Autowired
    private PdImageHistoryDao pdImageHistoryDao;
    @Autowired
    private PdBaseDao pdBaseDao;

    @Override
    public PdMerchandise getProduct(Integer id) {
        return pdMerchandiseDao.get(id);
    }

    @Override
    public PdCommodityMedia getPdCommodityMedia(Integer id) {
        if(id==null){
            return null;
        }
        return pdCommodityMediaDao.get(id);
    }

    @Override
    public PdCommodityMediaHistory getPdCommodityMediaHistory(Integer id) {
        return pdCommodityMediaHistoryDao.get(id);
    }

    @Override
    public PdImage getPdImage(Long id) {
        return pdImageDao.get(id);
    }

    @Override
    public PdImageHistory getPdImageHistory(Long id) {
        return pdImageHistoryDao.get(id);
    }

    @Override
    public PdBase getPdBase(Integer id) {
        return pdBaseDao.get(id);
    }
}
