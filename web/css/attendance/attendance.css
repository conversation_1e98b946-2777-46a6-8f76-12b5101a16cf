
/*creator:姚宗涛 date:before 2018/3/21*/
select{padding:0 12px;height: 30px;line-height: 30px;}
/*.collectionBtns .ty-opTTL>option{width:100px;}*/
.ty-mainData>.main_left{float:left;width:8%;max-width: 100px;}
.marTop{margin-top:42px;}
.ty-mainData>.main_left>.today{display: block;line-height: 40px;text-align: center;}
.ty-mainData>.main_right{float:left;width:92%;}
/*// 正五边形*/
span.ty-pentagon{ position:relative; width: 20px; height:20px; display: inline-block;   }
span.ty-pentagon>span{ width:11px; border-top:2px solid #333; position: absolute; transform-origin:left;   }
span.ty-pentagon>span:nth-child(1){ top:8px; transform: rotate(-36deg);    }
span.ty-pentagon>span:nth-child(2){ right:2px; top:8px; transform: rotate(36deg); transform-origin:right;  }
span.ty-pentagon>span:nth-child(3){ top:8px; transform: rotate(72deg);   }
span.ty-pentagon>span:nth-child(4){ top:8px; transform: rotate(-72deg); transform-origin:right;right: 2px;  }
span.ty-pentagon>span:nth-child(5){ top:18px; left:4px;   }
/*  菱形 */
.ty-diamond{display: inline-block;width:14px;height:14px;border:2px solid #333;transform:rotate(45deg);}
/*  三角形 */
.ty-triangle{position:relative;display: inline-block;width:15px;height:15px;}
.ty-triangle>span{width:15px; border-top:1px solid #333; position: absolute;}
.ty-triangle>span:nth-child(1){ top:8px;left:-8px; transform: rotate(-60deg); }
.ty-triangle>span:nth-child(2){ top:8px; transform: rotate(60deg); }
.ty-triangle>span:nth-child(3){ top:14px; left:-4px;}

/* 考勤设置页面 */
.tabCard{ height: 80%;margin-left:20px ;margin-top:20px;}
.tab_item{
    padding: 12px 24px;
    border: 1px dashed #5d9cec;
    background: #fff;
    font-size: 12px;
    line-height: 24px;
    color: #515151;
}
.setRoleBtn{ display: none;  }
.tab_item:hover .setRoleBtn{ display: block;  }
.tab_i{ display: inline-block ; margin: 0 64px 0 0 ;  }
.deleRole{ position: absolute; top: 10px; right:4px; padding:2px 5px; display: inline-block; cursor: default; color: #ed5565 }
.deleRole:hover{ background: #eee;  }

/*  选择合适部门弹框  */
#selectSuitableDerpartment .bonceCon div{float:left;}
#selectSuitableDerpartment .bonceCon ul li{width:100%;height:35px;line-height:35px;padding-left:15px;border-bottom:1px solid #A6A6A6;}
.leftDerpartment,.rightDerpartment{width: 160px;height: 260px;border:1px solid #A6A6A6;float:left;}
.leftDerpartment{margin-left: 20px;}
.leftDerpartment ul{width:100%;}
.leftDerpartment>li{width:100%;height:35px;line-height:35px;padding-left:15px;border-bottom:1px solid #A6A6A6;}
.leftDerpartment li div{width:100%;}
.leftDerpartment li div i{float:right;font-size: 26px;margin-right: 5px;}
/*  修改考勤页面 */
/*.topOperation>span{float:left;}*/
.topOperation .ty-opTTL>option{width:100px;}
.selectMonth{margin-left:2%;}
.attendMark>.fa{margin-left:36px;margin-top:20px;margin-right:5px;font-size: 20px;}
.updateAttendTab{float:left;width:50%;}
.dayList{float:left;width:50%;overflow-x: scroll;}
.dayList table thead tr td{border:1px solid #d7d7d7;white-space: nowrap;}
.dayList table tbody tr td i{cursor:pointer;}
.attendList{min-width:1051px;}
/*  考勤录入  考勤弹框  */
.fristSet{ background: #f8fad3; border:1px dashed #ccc; padding:20px 30px; }
#attendance .bonceCon>div{margin:0 auto;}
#attendance{width:495px;}
#attendance i{font-style: normal;}
.updaterInfo{margin-top:25px;}
.updaterInfo>p{border-bottom:1px solid #BFBFBF;margin-bottom:0;}
.updaterInfo>div dl dd{float:left; line-height: 30px; margin-right: 8px; min-width: 60px}
/*.updaterInfo>div dl dd>span,.updaterInfo p span{margin-left:20px;}*/
.updaterInfo>div dl{height:30px;margin-top:5px;}
.updaterInfo input/*,.updaterInfo select*/{width:77px;text-align: center}
.marleft{margin-left:-20px;margin-right: 10px;}
.outOfworkAdd{margin-top:-15px;}
.hide{display: none;}
#first_attendance .bonceHead .name{margin-right:20px;}

/*  修改考勤  查看请假记录  查看按钮弹框 / 查看加班记录  详情 查看按钮 弹框 / 修改考勤弹框 */
#details,#overtimeDetails{width:500px;}
#details p,#overtimeDetails p,#updateAttend p{border-bottom:1px solid #BFBFBF;margin-bottom:0;}
#details p,#fix_overtimeDetails p,#updateAttend p{border-bottom:1px solid #BFBFBF;margin-bottom:0;}
#details div>dl,#overtimeDetails div>dl,#updateAttend div>dl dd{float:left;margin-right: 3%;}
#details div>dl,#fix_overtimeDetails div>dl,#updateAttend div>dl dd{float:left;margin-right: 3%;}
#details div>dl dd,#overtimeDetails div>dl dd{height:30px;line-height:30px;}
#details div>dl dd,#fix_overtimeDetails div>dl dd{height:30px;line-height:30px;}
#details .bonceCon{max-height:460px;overflow-y: auto}
/*  修改考勤  修改老勤弹框  */
#updateAttend{width:705px;}
#updateAttend p{line-height:32px;margin-bottom:10px;}
#updateAttend p>span{margin-right:29px;}

/*  考勤设置 选择合适部门弹框  */
.ty-panel{  background: none;    }
.ty-colFileTree{ float: left ; max-height:700px; width:310px;    }
.mar{ margin-left: 330px;  }
.fa-folder+span{ width:100px; display:inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
.departTree{  }
.departTree>ul , .departTree>form{ width: 315px; padding:15px 0; margin-left: 10px; border:1px solid #ccc; position: relative;
    box-shadow: 0 0 3px #ccc;height:300px; overflow:auto;   }
.departTree > form {  margin-left: 80px; }
.departTree i.fa{  color: #4E9DFF; font-weight: bolder; margin:0 20px;   }
.departTree li>div{ position: relative; padding:3px 0 3px 25px; height:30px;    }
.departTree li>div:hover{ background: #eaeaea; box-shadow:0 0 2px #ccc; cursor: pointer; color: #333; font-weight: bold;     }
.departTree li>div>span:hover{  }
.departTree li>div>i.fa-angle-right ,.departTree li>div>i.fa-angle-down , .departTree li>div>i.fa-info{ position:absolute; left:-10px; font-size:16px;     }
.departTree li>div>i.fa-plus-square:hover ,.departTree li>div>i.fa-minus-square:hover , .departTree li>div>i.fa-angle-right:hover{ color:#0075ff;    }
.departTree li>div>i.fa-plus-square, .departTree li>div>i.fa-minus-square{ float: right;     }
.departTree ul ul{ display: none;padding-left:15px;  }
#scanSet { width:760px;  }
.arrow{  color: #4E9DFF; font-size:50px; top:95px; width:75px; position: relative;    }
#all .ty-gray , #nowRight .ty-gray { color:#aaa; }

.fa-long-arrow-right {  color: #ed5565 !important;  font-size: 45px;  top: 65px;  position: relative;  }
.bigT {  color: #ed5565;  font-size: 25px;  top: 65px;  position: relative;  text-align: center;  }

/*creator :lyt date:2018/03/21*/
.overflow{overflow:hidden;}
.businessTime .ty-left{width:40%;}
.tab_item ul li{line-height:40px;}
.setting{float:left;margin-left:40px;}
.busyTimeSetting { position: relative;  }
.busyTimeSetting .mask {  position: absolute;  width: 100%;  height: 100%;  background: rgba(220, 240, 240, 0.3);  left: 0;  top: 0;  cursor: no-drop;  }
.busyTimeSetting li{ line-height:40px;}
.busyTimeSetting .smallTitle{ display:inline-block; width: 100px ;padding: 0 8px; }
.radioSelect{ margin-right:12px;  }
span.ty-gray{ color: #aaa!important;  }
.department{ vertical-align:top;cursor: pointer; border: 1px solid #d7e2ec; padding: 4px 6px;min-height:30px; line-height: normal;}
.addDepart{ display: block;float:right; height:100%;padding: 2px 4px;  margin: 4px; }
.addDepart:hover{ background: #eee }
.departItem{ border: 1px solid #ddd; display: inline-block; margin:3px 0; padding:2px 0 3px 5px; background: #fff;border-radius:4px; color:#666;  }
.departItem:hover{ border: 1px solid #ed5565; color:#333;   }
.departItem .fa-close{ color:#48cfad;   }
.departItem:hover .fa-close{ color:#ed5565;   }
.departSelected{float:left;width:90%;}
.deparSelected{ color:#ed5565;  }
.clockTime .select2-container--default .select2-selection--single{/*width:130px;*/}
.select2-container--default .select2-selection--single{border:1px solid #98e0ce;}
.select2-container--default .select2-selection--single .select2-selection__rendered{line-height:30px;}
.select2-container--default .select2-selection--single .select2-selection__arrow{height:30px;}
.select2-container .select2-selection--single{height:30px;}
.mar60{margin-right:60px;}
.departSelected span i{margin-right:6px;display:inline-block;width:18px;font-style:normal;text-align:center;cursor:pointer;}
.bridge{display:inline-block;width:60px;text-align:center;}
.layer{margin-bottom:12px;padding: 10px 16px; border: 1px solid #d7e2ec; background: #fff}
.gap10{width:700px;}
.gap20{margin-right:86px;}
.gap50{display:inline-block;width:48%;}
.defined_dl {margin-bottom:30px;}
.defined_dl p{border-bottom: 1px solid #BFBFBF;}
.defined_ul li{float:left;width:48%;}
.defined_ul li:nth-child(odd){margin-right:4%;}
.title_dl{margin-right:30px;}
.xing::after {  content: "*";  width: 5px;  height: 5px;  display: inline-block;  color: red;  font-weight: bolder;  position: relative;  }
.commenWind dl dd{line-height:30px;margin-bottom:6px;}
/*.biao input{border:1 px solid }*/

/*canlendar部分*/
td.rest{  color:#ed5565;}
.holiday_sign{  position: absolute;  left: 2px;  top: 2px;  display: block;  width: 15px;  height: 15px;  color: #fff;  text-align: left;
    text-indent: 1px;  line-height: 14px;  overflow: hidden;  }
td.rest .holiday_sign{  background: #f43;  }
td.work .holiday_sign{  background: #969799;  }
td.active .fa-minus{ background: #969799; color: #fff; }
.clendars{ margin-bottom:32px; }
.clendars table td{  text-align:center;  height:40px;  line-height: 40px;  padding:0;  }
.timeinput{ width:250px; border: 1px solid #48cfad!important; ;  }
.clendars table thead td{  color: #000; font-weight: bold;  }
.clendars td[disabled]{ background:#e6e4e4; color:#666;  }
.clendars table{ display: none; }
.clendars table:first-child{ display: table; }
.day-rel{  position:relative;  }
.defineColor{  background-color: #9beae4;  }
.otherSetting{  margin-top:20px;  }
.detailsHead{  margin-bottom:12px;  }
.compareSect{  margin:30px 0;  padding-top:10px;  border-top:1px solid #ccc;  }
.sect-title{  width:100px;  text-align:right;  position: absolute;  left: 0;  }
.detCon{  width:45%;  }
.detCon ul li{  position: relative;  line-height:34px;  padding-left: 112px;  }
.editAfter{  background: rgba(254, 17, 24, 0.53);  position:relative;  }
.editAfter .flag_arrow{  position: absolute;  left: -77px;  color: rgba(254, 17, 24, 0.53);  display: inline-block;  width: 55px;  font-size: 52px;  bottom: 16px;  line-height: 0px;  }
.atTip{ font-size:0.6em ; color:#aaa;   }
.atTip>span{ color: red;   }
/* 部门选择  */
.ty-panel{  background: none;    }
.ty-colFileTree{ float: left ; max-height:700px; width:310px;    }
.mar{ margin-left: 330px;  }
.fa-folder+span{ width:100px; display:inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
.departTree>ul , .departTree>form{ width: 315px; padding:15px 0; margin-left: 10px; border:1px solid #ccc; position: relative;
    box-shadow: 0 0 3px #ccc;height:300px; overflow:auto;   }

#scanSet { width:760px;  }
.arrow{  color: #4E9DFF; font-size:50px; top:95px; width:75px; position: relative;    }
#allRight .ty-gray , #nowRight .ty-gray { color:#aaa; }

.fontWeight{font-weight:800;}
#attenceOtherSetting td.active{ cursor: pointer ;  }
#yearOption{margin-right:20px;}
#roles .noonTime select{ width: 220px; }
.updateAttendTab thead td{border:1px solid #d7d7d7;}
.clendars td { cursor: pointer;  }
#timetableSetting .edit, #timetableSetting .mask, #updateAT {  display: none;  }
#timetableSetting .bonceCon {  position: relative;  }
#timetableSetting .mask {  position: absolute;  width: 470px;  height: 290px;  bottom: 9px;  background: rgba(200, 200, 200, 0.1);  }
#updateAT {  text-align: left;  }
#updateAT input {  display: inline-block;  height: 35px;  line-height: 35px;  border: 1px solid #ccc;  padding: 0 10px;  }
.addOut,.addLeave,.addOverTime{margin-right:20px;}
#updateAttend dl{margin-bottom:0;}
#updateAttend .commenWind{margin-bottom:20px;}
.updateOver dl:after,.updateOver dl:before,.updateOut dl:before,.updateOut dl:after{display:block;  clear:both;  content:"";  visibility:hidden;  height:0}
.updateLeave dl:after,.updateLeave dl:before,.clearFlt:before,.clearFlt:after{display:block;  clear:both;  content:"";  visibility:hidden;  height:0}
.updateOver dl dd:first-child{width:62px;}
#attendDetList {height: calc(100% - 60px); overflow-y: auto}
#attendDetList i{font-style:normal;display:inline-block;width:14px;}
#departSelect{overflow-x: auto;}
#overtimeDetails .bonceCon,#fix_overtimeDetails .bonceCon{max-height:620px;overflow-y:auto;}
.leaveMemo,.overTimeReason{max-width:150px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis}
.ellipsis{max-width:200px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis}
.attendListCon .ty-table thead td{border-color: #d7d7d7;}
.leaveDetails{margin-bottom: 12px;}
.leaveDetails tr td{padding: 5px 10px;  text-align: left;  white-space: nowrap;}
.selectCount{  border-bottom: 1px solid #e0e0e0; margin-bottom: 8px}
.ssPanel input,.ssPanel select{padding-left: 10px;width: 130px;line-height: 26px;}
.col-lt-4 {float:left; width: 33.3%;}
.col-rt-4 {float:right; width: 33.3%;text-align: right;}
.ssTtl{margin-right: 4px;}
.cal-date{margin-right: 4px;}
#attendCountList thead tr td, #attendDetList thead tr td{border-color: #d7d7d7;}
.ty-alert{
  font-size: 14px;
}
.ty-circle-2{
    border-radius: 2px;
}
.kj-input, .kj-select{
    height: 32px;
    border:1px solid #f0f0f0;
    color: #666;
    padding:0 8px;
    min-width: 167px;
}
.kj-input-blue, .kj-select-blue{
    border-color: #5d9cec;
}
.kj-input:disabled, .kj-select:disabled{
    border-color: #ccc;
    background-color: #f0f0f0;
    cursor: not-allowed;
    color: #aaa;
}
.btn-group{
    flex: auto;
    position: relative;
}
.kj-table{
    min-width: 100%;
    table-layout: fixed;
    border-collapse: separate;
    border-left:1px solid #ebeef5;
    border-top:1px solid #ebeef5;
    z-index: 10;
}
.kj-table.text-center{
    text-align: center;
}
.kj-table thead{
    background-color: #f2f2f2;
    color: #707277;
    font-weight: 500;
}
.kj-table th, .kj-table tbody td{
    padding: 10px 4px;
    box-sizing: border-box;
    text-overflow: ellipsis;
    vertical-align: middle;
    position: relative;
    border-bottom: 1px solid #ebeef5;
    overflow: hidden;
    background-color: #fff;
    border-right: 1px solid #ebeef5;
    text-align: center;
}
.kj-table th{
    background-color: #f4f6f9;
    color: #707277
}
.kj-table.fixed th{
    position:-webkit-sticky; position:sticky;
    z-index: 10;
}
.kj-table.fixed thead {
    position:-webkit-sticky; position:sticky;
    top:0;
    z-index: 20;
}
.kj-table.fixed thead th.fixed{
    left: 0;
    z-index: 30;
    width: 60px;
    min-width: 60px;
}
.kj-table.fixed thead th.fixed2{
    left: 60px;
    z-index: 30;
    width: 90px;
    min-width: 90px;
}
.kj-table.fixed thead th.fixed3{
    left: 150px;
    z-index: 30;
    width: 60px;
    min-width: 60px;
}
.kj-table.fixed tbody th.fixed{
    left: 0;
}
.kj-table.fixed tbody th.fixed2{
    left: 60px;
}
.kj-table.fixed tbody th.fixed3{
    left: 150px;
}

.kj-table tbody td{
    color: #8f9299;
}
.kj-table tbody tr:nth-of-type(even) td{
    background:#fcfcfc;
}
.kj-table tbody td.hover:hover{
    color: #5d9cec;
    cursor: pointer;
}
.attendanceQuery{
    margin-top: 8px;
}
.queryItem{
    display: inline-block;
    width: 400px;
}
.queryItem input{
    width: 180px;
}
.queryItem select{
    width: 180px;
}
.icon_mark{
    margin: 0 8px;
}
.table-fixed-avatar{
    width: 100%;
    overflow: auto;
}
.tab_item b{
    display: inline-block;
    vertical-align: bottom;
    width: 110px;
}
.tab_item span{
    color: #5d9cec;
}
.ty-tblContainer{
    margin-top: 8px;
}
.detail table.kj-table th,.detail table.kj-table td{
    text-align: center;
}

.bounceItem{
    display: flex;
    padding: 4px 0;
}
.bounceItem .bounceItem_title{
    width: 90px;
    flex: none;
    color: #666;
    line-height:32px;
}
.bounceItem .bounceItem_content{
    margin-bottom: 4px;
    flex: auto;
    line-height:32px;
}
.bounceItem .bounceItem_content .ty-radio, .bounceItem .bounceItem_content .ty-checkbox{
    width: 120px;
    padding: 0 24px;
}
.bounceItem .bounceItem_content .ty-checkbox{
    border-radius: 3px;
    cursor: default;
}
.bounceItem .bounceItem_content .ty-checkbox:hover, .bounceItem .bounceItem_content .ty-checkbox.checked{
    background: #daebff;
}
.bounceItem .bounceItem_content input + label{
    left: 24px;
    top: calc(50% - 8px);
}
#leaveRecord .bounceItem .bounceItem_title{
    width: 100px;
}
.inputInfo_avatar{
}
.inputInfo_item{
    position: relative;
}
.ty-hr{
    margin: 8px 0;
}
#updateAttend select{
    width: 100%;
}
#leaveRecord .bounceItem .bounceItem_title, #leaveRecord .bounceItem .bounceItem_content{
    line-height: 18px;
    margin-bottom: 0;
}
.input_model{
    display: none;
}
.process{
    font-size: 12px;
    color: #666;
    margin-top: 8px;
    margin-left: 100px;
}
@media screen and (max-width: 1367px) {
    .ty-btn-big{
        padding: 2px 12px;
        height: inherit;
        line-height: 1.5;
        font-size: 12px;
    }
    .kj-input, .kj-select{
        height: 22px;
    }
    .ty-alert{
        min-width: inherit;
        font-size: 12px;
    }
    .table-fixed-avatar {
        height: 450px;
        overflow-y: auto;
    }
}
.otherDepar{
    display: inline-block;
    width: 500px;
    text-align: right;
}
.processList .processName{
    display: inline-block;
    width: 48px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    vertical-align: bottom;
}
.textMax{
    line-height:1.2;
    font-size: 12px;
}
.kj-panel{
    border-top:1px solid #ddd;
}
#attendanceChangeDetail .kj-table th, #attendanceChangeDetail .kj-table tbody td {
    text-align: left;
    padding: 10px 20px;
}
.changeBtn{
    position: absolute;
    right: 21px;
    top: -5px;
}
.title-big{
    font-size: 16px;
    color: #666;
}
.gapMid {
    margin: 0 60px;
}
.modeSelect {
    margin-left: 50px;
}
.modeSelect >span{
    margin-left: 100px;
    padding: 10px;
}
.modeSelect .fa{
    margin-right: 10px;
}
.blueLink{
    color: #0070bf;
    font-weight: bold;
}
.panelCon{
    padding: 26px 0;
    border-top: 1px solid #cecece;
}
.panelCon:first-child{
    border-top: none;
}
.dailyView .gapL,.gapL{
    margin-left: 32px;
}
.gapFar{
    margin-left: 100px;
}
.blueCare{
    color: #0070bf;
}
.sdLine{
    margin-top: 20px;
    padding-top: 30px;
    border-top: 1px solid #bebebe;
}
.ckWrap{
    margin: auto;
    width: 80%;
}
.ckWrap p{
    padding-bottom: 6px;
}
.leaveCase .fa{
    padding-right: 30px;
}
.wrapLab{
    margin: auto;
    width: 900px;
}
.noDuty{
    padding-bottom: 42px;
}
.page:not(.main) {
    display: none;
    margin-left: 100px;
    padding-right: 100px;
}
.attendanceSet .ty-alert{
    color: #101010;
    justify-content: space-between;
}
.onEquip{
    display: none;
}
.dailyView > span{
    margin-left: 50px;
}
#first_roleCon{
    margin-top: 10px;
}
.weekdayCheck > div{
    margin-bottom: 20px;
}
.weekdayCheck .fa,.weekdayEdit .fa{
    margin-right: 20px;
}
.tab_con {
    margin-top: 20px;
}
#attendanceTime{
    width: 190px;
}
.smCon input{
    margin-right: 20px;
    width: 100px;
}
.dutyItem{
    width:220px;
    font-size: 14px;
    float: left;
    margin: 0 30px 10px;
    position: relative;
}
.busyTimeSetting .dutyItemLong{
    width: 500px;
}
.busyTimeSetting select, .busyTimeSetting input{
    width: 100%;
}
.dutyItem>.dutyItemTitle{
    margin: 8px 0;
    text-align: left;
}
.dutyItem>.dutyItemCon{
    width: 240px;
    height: 32px;
    background-color: #fff;
    border-radius: 5px;
    line-height: 31px;
    text-align: center;
    color: #333;
}

.isNeed, #updateAT{
    display: none;
}
.isSetNoon span{
    margin-left: 100px;
    margin-right: 10px;
}
.ty-alert .arrowBtn{color: #0070bf;}
.sdLine .ty-alert .fa{color: #0070bf;}
.modeSelect span.disabled{background: #ccc;}
.gapF{margin-left: 5px;}
.gapTp{margin-top: 50px;}
.viewTtl{display: inline-block;width: 200px;}
.equipCon1,equipCon0{margin-top: 20px;}
#equipList1,#equipList0{margin-top: 20px;}
.gapBt{margin-bottom: 50px;}
.byDevicesInfo{margin-top: 50px;}
.wrapperCon{margin: auto;width: 90%;color: #101010;}
.spaceT{margin-top: 50px;}
.spaceBt{margin-bottom: 20px;}
.blueFont{color: #0070bf;}














