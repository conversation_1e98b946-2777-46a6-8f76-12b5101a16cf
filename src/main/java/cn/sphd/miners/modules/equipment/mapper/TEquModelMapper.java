package cn.sphd.miners.modules.equipment.mapper;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.equipment.entity.TEquModel;
import org.springframework.stereotype.Repository;

import javax.persistence.MapKey;
import java.util.List;
import java.util.Map;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-02-10
 */
@Repository
public interface TEquModelMapper 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TEquModel selectTEquModelById(Long id);

    /**
     * 查询列表
     * 
     * @param tEquModel 
     * @return 集合
     */
    public List<TEquModel> selectTEquModelList(TEquModel tEquModel);

    /**
     * 新增
     * 
     * @param tEquModel 
     * @return 结果
     */
    public int insertTEquModel(TEquModel tEquModel);

    /**
     * 修改
     * 
     * @param tEquModel 
     * @return 结果
     */
    public int updateTEquModel(TEquModel tEquModel);

    /**
     * 删除
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTEquModelById(Long id);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTEquModelByIds(String[] ids);

    List<Map<String, Object>> selectTEquModelGroupList(TEquModel tEquModel);

    List<Map<String, Object>> selectEquModelDetailList(TEquModel tEquModel);
    List<Map<String, Object>> selectEquModelDetailListNoGroup(TEquModel tEquModel);

    Map getModelDetail(Long id);

    List<TEquModel> selectTEquModelListByCategory(TEquModel model);
}
