package cn.sphd.miners.modules.accountantReportTax.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxReportHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTriflesHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTriflesPeriodHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctReportHistoryEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxReportHistoryEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesHistoryEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesPeriodHistoryEntity;
import org.hibernate.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

@Repository
public class AcctTriflesHistoryDaoImpl extends BaseDao<AcctTriflesHistoryEntity, Serializable> implements AcctTriflesHistoryDao {

    @Autowired
    AcctTriflesHistoryDao triflesHistoryDao;
    @Autowired
    AcctTriflesPeriodHistoryDao triflesPeriodHistoryDao;

    @Override
    public void updateName(String name, int triflesId) {
        String hql = "update AcctTriflesHistoryEntity set name=:name where trifles=:trifles";
        HashMap<String, Object> param = new HashMap<>();
        param.put("name",name);
        param.put("trifles",triflesId);
        triflesHistoryDao.queryHQLWithNamedParams(hql,param);

    }

    @Override
    public List<AcctTriflesHistoryEntity> triflesUpdateRecord(Integer org, int triflesId) {
        String hql = "from AcctTriflesHistoryEntity where trifles=:trifles";
        HashMap<String, Object> param = new HashMap<>();
        param.put("trifles",triflesId);
        List<AcctTriflesHistoryEntity> list = triflesHistoryDao.getListByHQLWithNamedParams(hql,param);
        for (AcctTriflesHistoryEntity triflesHistoryEntity : list) {
            Integer hisId = triflesHistoryEntity.getId();
            List<AcctTriflesPeriodHistoryEntity> periodList = triflesPeriodHistoryDao.getListbytriflesId(hisId);
            triflesHistoryEntity.setPeriodHistoryList(periodList);
        }
        return list;
    }

    @Override
    public AcctTriflesHistoryEntity getLastByReportId(Integer triflesId) {
        String hql = " from AcctTriflesHistoryEntity where trifles=" + triflesId + " order by createDate desc";

        Query query = getSession().createQuery(hql);

//        query.setFirstResult(pageNum);
        query.setMaxResults(1);
        List<AcctTriflesHistoryEntity> list = query.list();
        AcctTriflesHistoryEntity triflesHistoryEntity = list.get(0);
        return triflesHistoryEntity;
    }
}
