package cn.sphd.miners.modules.complaint.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2018-01-12 15:23
 * @description
 **/
@Entity(name = "ComplaintAttachment")
@Table(name = "t_complaint_attachment")
public class ComplaintAttachment implements Serializable{

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "complaint")
    private Integer complaint;

    @Column(name = "supplement",insertable=false, updatable=false)
    private Integer supplement;

    @Column(name = "complaint_detail")
    private Integer complaintDetail;

    @Column(name = "category")
    private String category;

    @Column(name = "type")
    private String type;

    @Column(name = "title")
    private String title;

    @Column(name = "description")
    private String description;

    @Column(name = "path")
    private String path;

    @Column(name = "orders")
    private Integer orders;

    @Column(name = "memo")
    private String memo;

    @Column(name = "creator")
    private Integer creator;

    @Column(name = "create_name")
    private String createName;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "updator")
    private Integer updator;

    @Column(name = "update_name")
    private String updateName;

    @Column(name = "update_time")
    private Date updateTime;


    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="complaint_detail", referencedColumnName = "id" , nullable=false , unique=false , insertable=false, updatable=false)
    private ComplaintDetail detailAttachment;

    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="supplement", referencedColumnName = "id" , nullable=false , unique=false , insertable=true, updatable=true)
    private ComplaintSupplement supplementAttachment;

    //waijian


    public ComplaintDetail getDetailAttachment() {
        return detailAttachment;
    }

    public void setDetailAttachment(ComplaintDetail detailAttachment) {
        this.detailAttachment = detailAttachment;
    }

    public ComplaintSupplement getSupplementAttachment() {
        return supplementAttachment;
    }

    public void setSupplementAttachment(ComplaintSupplement supplementAttachment) {
        this.supplementAttachment = supplementAttachment;
    }

    //shujuku
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getComplaint() {
        return complaint;
    }

    public void setComplaint(Integer complaint) {
        this.complaint = complaint;
    }

    public Integer getSupplement() {
        return supplement;
    }

    public void setSupplement(Integer supplement) {
        this.supplement = supplement;
    }

    public Integer getComplaintDetail() {
        return complaintDetail;
    }

    public void setComplaintDetail(Integer complaintDetail) {
        this.complaintDetail = complaintDetail;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
