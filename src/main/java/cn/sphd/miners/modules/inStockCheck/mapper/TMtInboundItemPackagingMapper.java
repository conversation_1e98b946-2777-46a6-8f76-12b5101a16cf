package cn.sphd.miners.modules.inStockCheck.mapper;
import java.math.BigDecimal;
import java.util.Date;

import java.util.List;

import cn.sphd.miners.modules.inStockCheck.entity.TMtInboundItemPackaging;
import org.apache.ibatis.annotations.Param;

public interface TMtInboundItemPackagingMapper {
    int deleteByPrimaryKey(Integer id);

    int deleteByPrimaryKeyIn(List<Integer> list);

    int insert(TMtInboundItemPackaging record);

    int insertOrUpdate(TMtInboundItemPackaging record);

    int insertOrUpdateSelective(TMtInboundItemPackaging record);

    int insertSelective(TMtInboundItemPackaging record);

    TMtInboundItemPackaging selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TMtInboundItemPackaging record);

    int updateByPrimaryKey(TMtInboundItemPackaging record);

    int updateBatch(@Param("list") List<TMtInboundItemPackaging> list);

    int updateBatchUseMultiQuery(@Param("list") List<TMtInboundItemPackaging> list);

    int updateBatchSelective(@Param("list") List<TMtInboundItemPackaging> list);

    int batchInsert(@Param("list") List<TMtInboundItemPackaging> list);

    int batchInsertSelectiveUseDefaultForNull(@Param("list") List<TMtInboundItemPackaging> list);

    List<TMtInboundItemPackaging> findByAll(TMtInboundItemPackaging tMtInboundItemPackaging);



}