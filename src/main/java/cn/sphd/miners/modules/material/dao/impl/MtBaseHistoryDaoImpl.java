package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.commodity.entity.PdCompositionMaterialHistory;
import cn.sphd.miners.modules.material.dao.MtBaseHistoryDao;
import cn.sphd.miners.modules.material.entity.MtBase;
import cn.sphd.miners.modules.material.entity.MtBaseHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
@Repository
public class MtBaseHistoryDaoImpl extends BaseDao<MtBaseHistory, Serializable> implements MtBaseHistoryDao {

    @Override
    public int insert(MtBase mtBase){
        MtBaseHistory history=new MtBaseHistory();

        BeanUtils.copyProperties(mtBase,history);
        history.setId(null);
        history.setMaterial(mtBase.getId());
        save(history);
        getSession().flush();

        return history.getId();
    }

    @Override
    public int insert2(MtBase mtBase) {
        MtBaseHistory history=new MtBaseHistory();

        BeanUtils.copyProperties(mtBase,history);
        history.setId(null);
       // history.setMaterial(mtBase.getId());
        save(history);
        getSession().flush();
        return history.getId();
    }

    @Override
    public MtBaseHistory getNew(Integer  material) {
        String hql="from MtBaseHistory where material="+material+"  and (operation='1' or operation='2') order by id desc";
        return getByHQL(hql);
    }
}
