<template>
  <div id="potentialToFormal" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="产品详情" ></TY_NavTop>
    <div class="container">
      <div class="panel panel_product">
        <h4 class="h4_title">{{dataInfo.data.product.name}}</h4>
        <div>所用模板 <span class="subtitle">{{dataInfo.data.mpTmpl.name}}</span></div>
      </div>
      <div class="panel">
        <div class="ui-alert">已重命名模块的数量 <b class="renameNumber">{{dataInfo.data.reNameList.length}}</b> 个</div>
        <table class="ui-table ui-table-striped" style="width: 70%">
          <thead>
          <tr>
            <td>原名称</td>
            <td>新名称</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item, index) in dataInfo.data.reNameList" v-bind:key="index">
            <td>{{item.name}}</td>
            <td>{{item.newName}}</td>
          </tr>
          </tbody>
        </table>
        <div class="ui-alert" style="margin-top: 16px">
          主套餐内的模块
        </div>
        <table class="ui-table ui-table-striped">
          <thead>
          <tr>
            <td>模块名称</td>
            <td>下辖的一级菜单数量</td>
            <td>操作</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item, index) in dataInfo.data.mainModuleList" v-bind:key="index">
            <td class="name">{{item.name}}</td>
            <td>{{item.topMenu || '--'}}个</td>
            <td>
              <span class="link-blue" @click="seeModuleDetail(item)">查看</span>
            </td>
          </tr>
          </tbody>
        </table>
        <div class="ui-alert" style="margin-top: 16px">
          使用本模板的用户增值功能的模块
        </div>
        <table class="ui-table ui-table-striped">
          <thead>
          <tr>
            <td>模块名称</td>
            <td>下辖的一级菜单数量</td>
            <td>操作</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item, index) in dataInfo.data.increaseModuleList" v-bind:key="index">
            <td class="name">{{item.name}}</td>
            <td>{{item.topMenu || '--'}}个</td>
            <td>
              <span class="link-blue" @click="seeModuleDetail(item)">查看</span>
            </td>
          </tr>
          </tbody>
        </table>
        <div class="ui-alert" style="margin-top: 16px">
          与本模板增值功能模块对应的已有套餐
        </div>
        <table class="ui-table ui-table-striped">
          <thead>
          <tr>
            <td>套餐名称</td>
            <td>操作</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item, index) in dataInfo.data.mpSetList" v-bind:key="index">
            <td class="name">{{item.name}}</td>
            <td>
              <span class="link-blue" @click="seeMenuDetail(item)">查看</span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<style scoped>
.h4_title{
  font-size: 16px;
  margin: 8px 0;
  color: #333;
}
.subtitle{
  color: #333;
}
</style>

<script>
  export default {
    name: 'potentialToFormal',
    data () {
      return {
        loading: true,
        dataInfo: {
          approvalProcess: [],
          data: {
            product: {
              name: ''
            },
            mpTmpl: {
              name: ''
            },
            mainModuleList: [],
            increaseModuleList: [],
            mpSetList: [],
            reNameList: []
          }, // 用来展示的数据
          old: {}, // 用来切换修改前后的数据，切换时赋值到data中
          new: {} // 用来切换修改前后的数据，切换时赋值到data中（默认显示此数据）
        },
        reason: ''
      }
    },
    created () {
      let that = this
      let id = this.$route.params.id
      this.axios.post('../../../thali/getMpPackagesInfo.do', {
        packagesId: id
      })
        .then(function (response) {
          console.log(response)
          let data = response.data.data
          let thisData = {
            approvalProcess: data.approvalProcess || {},
            data: {}, // 用来展示的数据
            old: {}, // 用来切换修改前后的数据，切换时赋值到data中
            new: {} // 用来切换修改前后的数据，切换时赋值到data中（默认显示此数据）
          }
          thisData.new = {
            product: {
              name: data.mpPackages.name
            },
            mpTmpl: data.mpTmpl || {},
            reNameList: data.reNameList || [],
            mainModuleList: data.mainModuleList || [],
            increaseModuleList: data.increaseModuleList || [],
            mpSetList: data.mpSetList || []
          }
          thisData.data = thisData.new
          that.dataInfo = thisData
          that.loading = false
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    methods: {
      seeModuleDetail: function (item) {
        this.$router.push({
          path: `/moduleDetail`
        })
        localStorage.setItem('moduleInfo', JSON.stringify(item))
      },
      seeMenuDetail: function (item) {
        this.$router.push({
          name: 'menuDetail'
        })
        localStorage.setItem('menuInfo', JSON.stringify(item))
      }
    }
  }
</script>
