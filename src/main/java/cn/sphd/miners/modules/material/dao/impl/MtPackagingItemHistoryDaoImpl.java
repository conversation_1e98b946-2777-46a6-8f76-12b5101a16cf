package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.material.dao.MtPackagingItemHistoryDao;
import cn.sphd.miners.modules.material.entity.MtPackagingItem;
import cn.sphd.miners.modules.material.entity.MtPackagingItemHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class MtPackagingItemHistoryDaoImpl extends BaseDao<MtPackagingItemHistory, Serializable> implements MtPackagingItemHistoryDao {
    @Override
    public MtPackagingItemHistory insert(MtPackagingItem item, Long structureHistoryId) {
        MtPackagingItemHistory history=new MtPackagingItemHistory();

        BeanUtils.copyProperties(item,history);
        history.setPackagingItem(item.getId());
        history.setPackagingStruct(structureHistoryId);
        history.setId(null);
        save(history);
        return history;
    }
}
