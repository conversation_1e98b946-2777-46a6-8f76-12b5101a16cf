.ty-table-control .ty-color-blue:hover, .ty-td-control .ty-color-blue:hover{
    color: #fff;
    background-color:#5d9cec;
    transition: all .2s;
}
.ty-table-control td:last-child span, .ty-table-control td:last-child a, td.ty-td-control span, td.ty-td-control a {
    font-size: 12px;
    font-family: 宋体;
    padding: 5px 15px 5px;
    border-radius: 3px;
    font-weight: bold;
    cursor: pointer;
}
.dfg{
    font-size: 12px;font-family: 宋体;padding: 5px 15px 5px;
    border-radius: 3px;font-weight: bold;cursor: pointer;

}

.window{
    min-width: 750px;display: block;position: fixed;left: 380px;top: 18.5px;
}

/*下面是新添加的样式*/
.addone{
    margin-left: 56px;width:286px;
}
.addtwo{
    width: 347px;
}
.addthree{
    width: 338px;
}
.addfour{
    width: 337px;
}
.addfive{
    width: 345px;
}

#batimptble{
    width: 550px;
}
.befone{
    overflow:inherit;
}

.centten{
    text-align: center;
}

.detel{
    text-align:left;padding:10px 0 8px;
}

.enableStr{
    text-align: right;
}

.gap{
    margin-bottom: 10px;
}
.gap-1{
    margin-left: 22px;
}

.leftlit{
    background: none;
}

.manayge{
    display: block;
}

.salet1{
    margin-left: -11px;
}

.thingbox{
    display: none;
}
.gapFar{
    margin-right: 22px;
}
#contentEnable{
    text-align: right;
    max-height: 100px;
    overflow: auto;
    margin-top: 10px;
    color: #666;
    font-size: 13px;
}
.supplerCon{
    margin-bottom: 10px;
}
#contentEnable .enName,#record_contentEnable .enName{
    display: inline-block;
    width: 60px;
    text-align: center;
}
.saleUploadBtn .uploadify-button{
    display: inline-block;
}
.btn-group{
    text-align: right;
    flex: auto;
}
.kj-table .kj-table{
    height: 100%;
}
.row-flex{
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}
#edit_panoramaBtn{
    margin-left: 36px;
}
#cScan .leftTab tbody tr td:first-child {
    max-width: 400px;
}
#cEndTip2 input[type="radio"] {
    width: auto;
}