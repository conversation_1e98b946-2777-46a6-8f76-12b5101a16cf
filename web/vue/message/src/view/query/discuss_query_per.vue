<template>
  <div id="discussQueryPer">
    <TY_NavTop :title="title"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="ui-tip">
          <div class="queryCondition">
            <div>您要查询：</div>
            <div class="queryContent">{{queryData.timeBegin|formatDay('YYYY年MM月DD日')}} - {{queryData.timeEnd|formatDay('YYYY年MM月DD日')}}期间</div>
            <div class="queryContent" v-if="paramQuery.eventType === 1">
              <span v-if="paramQuery.approveStatus === '2'">审批通过</span><span v-if="paramQuery.approveStatus === '3'">被驳回</span>的讨论发起申请
            </div>
            <div class="queryContent" v-else>
              <span v-if="!paramQuery.fromUser">您经手且<span v-if="paramQuery.approveStatus === '2'">审批通过</span><span v-if="paramQuery.approveStatus === '3'">审批驳回</span>的讨论发起申请</span>
              <span v-if="paramQuery.fromUser">{{queryData.fromUserName}}被<span v-if="paramQuery.approveStatus === '2'">审批通过</span><span v-if="paramQuery.approveStatus === '3'">审批驳回</span>的讨论发起申请</span>
            </div>
          </div>
        </div>
        <div class="sendContainer">
          <div>
            符合查询条件的数据共如下{{queryData.list.length}}条
          </div>
        </div>
      </div>
      <div>
        <div>
          <div class="ui-cells">
            <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in queryData.list" v-bind:key="index">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <div>{{item.title}}</div>
                  <div style="margin-left: 20px">讨论发起人 {{item.createName}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="ui-cell__ft"></div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
  .queryCondition{
    background: #fffaeb;
    font-size: 12px;
    padding: 8px;
    margin-bottom: 5px;
  }
</style>

<script>
export default {
  name: 'discussQueryPer',
  data () {
    return {
      loading: true,
      title: '',
      paramQuery: {},
      queryData: {
        timeBegin: 1560408078000,
        timeEnd: 1560408078000,
        list: []
      }
    }
  },
  created: function () {
    let paramQuery = this.$route.params.data
    if (!paramQuery) {
      paramQuery = JSON.parse(localStorage.getItem('query'))
    }
    this.paramQuery = paramQuery
    paramQuery.eventType === 3 ? this.title = '讨论发起审批' : this.title = '讨论发起申请'
    this.getDiscussFileByType()
  },
  methods: {
    jump: function (id) {
      this.$router.push({
        path: `/discussDetail/${id}/query${this.paramQuery.eventType}`
      })
    },
    getDiscussFileByType: function () {
      let that = this
      this.$http.post('../../../forum/findForumPost.do', this.paramQuery, {
        emulateJSON: true
      }).then((response) => {
        this.loading = false
        let data = response.body
        console.log('queryData' + data)
        if (data) {
          that.queryData = data.data
        } else {
          that.$kiko_message('加载失败')
        }
      }).catch(function () {
        // this.jumpError('系统错误，请重试！')
      })
    }
  }
}
</script>
