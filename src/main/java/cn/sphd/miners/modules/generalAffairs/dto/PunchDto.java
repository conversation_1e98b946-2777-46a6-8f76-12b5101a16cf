package cn.sphd.miners.modules.generalAffairs.dto;

import cn.sphd.miners.common.persistence.BaseEntity;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.generalAffairs.entity.PersonnelAttendancePunch;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService.PunchType;

import java.util.Date;
/**
 * PunchDto 用于打卡记录查询
 * <AUTHOR>
 * @since 3.9
 * @date 2025-06-11 12:44:08
 **/
public class PunchDto extends BaseEntity{

    //下面是 User 表
    private Integer userID;//员工id

    private String userName;//员工姓名
    private String department;//员工部门

    //下面是 Organization 表 (此处仅用于部门)
    private String name;//部门名称

    //下面是 PersonnelAttendancePunch 表
    private Integer id;//人事_员工打卡表id(PersonnelAttendancePunch.id)

    private String cardNo; //打卡卡号

    private Date punchTime;//打卡时间
    private String punchDateStr;//打卡日期
    private String punchTimeStr;//打卡时间
    private Byte punchType;//打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
    private String punchTypeStr;//打卡类型

    private String image;//快照图片内容

    //下面是PersonnelAttendanceTerminal表
    //1.342考勤打卡 2025-05-30 PersonnelAttendanceTerminal表弃用，仅保持兼容，数据直接从PersonnelAttendancePunch查询
    private Integer punchTerminal;//打卡终端ID(对应的t_personnel_attendance_terminal表id)
    private Byte type;//类型:1-设备,2-webchat
    private String terminalUuid; //唯一标识/webchat union_id

    private String brand;//品牌/昵称

    private String model;//型号

    //下面是 IotTerminal 表
    Integer sysDevice;//打卡设备id
    private String deviceUuid;//打卡设备唯一标识
    private String sn;//打卡设备编号

    public Integer getUserID() {
        return userID;
    }

    public void setUserID(Integer userID) {
        this.userID = userID;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public Date getPunchTime() {
        return punchTime;
    }

    public void setPunchTime(Date punchTime) {
        this.punchTime = punchTime;
        this.punchDateStr = NewDateUtils.dateToString(punchTime, "yyyy-MM-dd");
        this.punchTimeStr = NewDateUtils.dateToString(punchTime, "HH:mm:ss");
    }

    public Byte getPunchType() {
        return punchType;
    }

    public void setPunchType(Byte punchType) {
        this.punchType = punchType;
        this.punchTypeStr = PunchType.getByIndex(punchType).getName();
    }

    public String getPunchTypeStr() {
        return punchTypeStr;
    }

    public void setPunchTypeStr(String punchTypeStr) {
        this.punchTypeStr = punchTypeStr;
    }

    public String getPunchDateStr() {
        return punchDateStr;
    }

    public void setPunchDateStr(String punchDateStr) {
        this.punchDateStr = punchDateStr;
    }

    public String getPunchTimeStr() {
        return punchTimeStr;
    }

    public void setPunchTimeStr(String punchTimeStr) {
        this.punchTimeStr = punchTimeStr;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public Integer getPunchTerminal() {
        return punchTerminal;
    }

    public void setPunchTerminal(Integer punchTerminal) {
        this.punchTerminal = punchTerminal;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public String getTerminalUuid() {
        return terminalUuid;
    }

    public void setTerminalUuid(String terminalUuid) {
        this.terminalUuid = terminalUuid;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Integer getSysDevice() {
        return sysDevice;
    }

    public void setSysDevice(Integer sysDevice) {
        this.sysDevice = sysDevice;
    }

    public String getDeviceUuid() {
        return deviceUuid;
    }

    public void setDeviceUuid(String deviceUuid) {
        this.deviceUuid = deviceUuid;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public PunchDto() {
    }

    //1.342考勤打卡，本构造函数被 WorkAttendanceService.getPersonnelAttendancePunches 使用，如有变更请同步修改；
    public PunchDto(Integer userID, Integer id, String cardNo, Date punchTime, Byte punchType, Byte type, String terminalUuid, String brand, String model, String image, Integer sysDevice) {
        setUserID(userID);
        setId(id);
        setCardNo(cardNo);
        setPunchTime(punchTime);
        setPunchType(punchType);
        setType(type);
        setTerminalUuid(terminalUuid);
        setBrand(brand);
        setModel(model);
        setImage(image);
        setSysDevice(sysDevice);
    }

    //1.342考勤打卡，本构造函数用于打卡回显
    public PunchDto(PersonnelAttendancePunch pap) {
        setUserID(pap.getUser());
        setId(pap.getId());
        setCardNo(pap.getCardNo());
        setPunchTime(pap.getPunchTime());
        setPunchType(pap.getPunchType());
        setType(pap.getTerminalType());
        setTerminalUuid(pap.getTerminalUuid());
        setBrand(pap.getTerminalBrand());
        setModel(pap.getTerminalModel());
        setImage(pap.getImage());
        setSysDevice(pap.getSysDevice());
    }
}