<template>
  <div id="fileResult" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="即将消失的文件" isBackHome="false"></TY_NavTop>
    <div class="container">
      <div class="place">
        <div class="placeContainer">
          <div class="panel handle">
            <div class="handle_title">本文件 <span class="color-blue">{{fileDetail.fileValidTime}}</span></div>
            <div class="handle_content">
              <div>于{{fileDetail.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}由{{fileDetail.createName}}创建</div>
              <div>于{{removeRecord.createDate}}由{{removeRecord.createName}}移入“{{removeRecord.categoryName}}”</div>
              <div>于{{deleteRecord.createDate}}由{{deleteRecord.createName}}移入“即将消失的文件”</div>
            </div>
            <div class="handle_button">
              <input type="submit" class="ui-btn ui-btn_info" value="查看文件" @click="showDialog()"/>
              <input type="submit" class="ui-btn ui-btn_success" value="还原" @click="restore()"/>
            </div>
          </div>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="line-p">
            <div class="tt-cell">文件名称</div>
            <div class="con-cell" v-text="fileDetail.name"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">文件编号</div>
            <div class="con-cell" v-text="fileDetail.fileSn"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">文件类型</div>
            <div class="con-cell" v-text="fileDetail.version">xls</div>
          </div>
          <div class="line-p">
            <div class="tt-cell">文件大小</div>
            <div class="con-cell">{{bytesToSize}}</div>
          </div>
          <div class="line-p" v-if="fileDetail.changeNum">
            <div class="tt-cell">版本号</div>
            <div class="con-cell">G{{fileDetail.changeNum}}</div>
          </div>
          <div class="line-p" v-if="!fileDetail.changeNum">
            <div class="tt-cell">说明</div>
            <div class="con-cell" v-text="fileDetail.content"></div>
          </div>
          <div class="line-p" v-if="fileDetail.changeNum">
            <div class="tt-cell">换版原因</div>
            <div class="con-cell" v-text="fileDetail.content"></div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title="当前的有效文件"
      :visible.sync="pendingDialog"
      width="70%">
      <el-form class="preview">
        <el-form-item>
          <el-radio-group v-model="browser">
            <el-radio :disabled="seeOnlineDisabled" :label="1" size="large">在线预览</el-radio>
            <el-radio :label="2" size="large">下载</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <a class="fc-btn-normal" @click="pendingDialog = false">取消</a>
          <span class="fc-btn ui-btn_info" ref="operate" :path="fileHandleDetail.path" :download="fileHandleDetail.name + '.' + fileHandleDetail.version" @click="oprationFile($event)">确定</span>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { formatDate, canSeeOnline, gohistory } from '@/js/common'
  export default {
    name: 'willCleanFIleDetail',
    filters: {
      formatDate
    },
    data () {
      return {
        loading: true,
        pendingDialog: false,
        fileDetail: {},
        removeRecord: {},
        deleteRecord: {},
        browser: 0,
        fileHandleDetail: {},
        dialogTitle: '',
        seeOnlineDisabled: true
      }
    },
    computed: {
      bytesToSize () {
        let sizeStr = ''
        let fileSize = this.fileDetail.size
        sizeStr = fileSize < 102400 ? parseFloat(fileSize / 1024).toFixed(2) + 'KB' : parseFloat(fileSize / 1048576).toFixed(2) + 'MB'
        return sizeStr
      },
      filePosition () {
        let pos = this.categoryName
        let posArr = pos.split('/')
        let posStr = ''
        for (let a = 0; a < posArr.length; a++) {
          if (a === posArr.length - 1) {
            posStr += '<span class="font-orange">' + posArr[a] + '</span>'
          } else {
            posStr += posArr[a] + '/'
          }
        }
        return posStr
      }
    },
    created: function () {
      let fileId = this.$route.params.id
      let that = this
      this.axios.post('../../../res/getDelTrashFileMessage.do', {
        fileId: fileId
      })
        .then(function (response) {
          that.loading = false
          console.log(response)
          let getData = response.data.data
          that.fileDetail = getData.res
          that.removeRecord = getData.listTwoRecord[0]
          that.deleteRecord = getData.listTwoRecord[1]
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    methods: {
      oprationFile: function (event) {
        let el = event.currentTarget
        window.parent.previewOrDownload(el, this.browser)
      },
      showDialog: function () {
        let fileHandleDetail = {
          path: this.fileDetail.path,
          name: this.fileDetail.name,
          version: this.fileDetail.version
        }
        this.seeOnlineDisabled = !canSeeOnline(this.fileDetail.version)
        this.fileHandleDetail = fileHandleDetail
        this.pendingDialog = true
        if (this.seeOnlineDisabled) {
          this.browser = 2
        } else {
          this.browser = 1
        }
      },
      restore: function () {
        let that = this
        this.$confirm('文件“还原”后，回到为“暂时不用的文件”中。确定“还原”这个文件吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          that.loading = true
          this.axios.post('../../../res/delFileRestore.do', {
            fileId: that.$route.params.id
          })
            .then(function (response) {
              that.loading = false
              console.log(response)
              let state = response.data.data.state
              if (state === 1) {
                that.$kiko_message('操作成功')
                gohistory(that)
              } else {
                that.$kiko_message('无法还原，原文件夹已经删除')
              }
            })
            .catch(function (error) {
              console.log(error)
            })
        })
      }
    }
  }
</script>
