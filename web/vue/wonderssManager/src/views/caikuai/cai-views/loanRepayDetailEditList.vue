
<template>
  <div>
    <div class="head">
      <div style="width: 100%">
        <PageHeader title="财会" :showBack="true" :showClose="true" @mycloseFun="closeFun"></PageHeader>
      </div>
    </div>
    <div>
      <h3>修改记录</h3>
      <ul class="xiangUlB padding1015">
        <li v-if="list.length > 0" >当前资料为第{{ list.length }}次修改后的结果。</li>
        <li v-else>当前资料尚未经修改。</li>
        <li v-if="list.length > 0" >修改人 {{ listLast.createName }} {{ new Date(listLast.createDate).format('yyyy-MM-dd hh:mm:ss') }}</li>
        <li v-else >创建人 {{ info.createName }} {{ new Date(info.createDate).format('yyyy-MM-dd hh:mm:ss') }}</li>
      </ul>
      <div class="hr_gray"></div>
      <ul class="repItem "  v-if="list.length > 0" v-for="(item,index) in list" :key="index">
        <li @click="goEditLogDetails(item,index)"><span class="greenTxt">{{ index === 0 ? '原始信息' : '第'+ index + '修改后'  }}</span> </li>
        <li class="endLi">{{ index === 0 ? '创建人' : '修改人'  }} {{ item.createName }} {{ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }}</li>
      </ul>
      <div class="hr_gray"></div>
    </div>


  </div>

</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  data() {
    return {
      list: [],
      listLast:{},
      info:{}
    }
  },
  mixins: [finance],
  mounted() {
    this.editLog()
  },
  methods: {
    editLog(){
      let info = JSON.parse(localStorage.getItem('loanRepayEditList'))
      this.info = info
      this.financeApi.ordRecordModHistoryList({ id : info.id}).then(res1=>{
        let res = res1.data.data
        this.list = res
        console.log('list=', res)
        if(res.length > 0){
          this.listLast = res[res.length -1]
        }

      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })
    },
    goEditLogDetails(item,index){
      item.index = index
      localStorage.setItem('loanRepayEditDetail', JSON.stringify(item))
      this.$router.push({ path:`/loanRepayEditDetail` })
    },


  },
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";
h3{
  text-align: center; line-height: 50px;
}
.xiangUlB:nth-child(odd){ background-color: #f0f0f0; }

</style>