<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.TAccountantSubjectCashHistoryMapper" >

  <select id="getSingle" parameterType="subjectCashHistory" resultType="subjectCashHistory" >
    select 
        id, detail_history_id AS detailHistoryId, detail_id AS detailId, cash_flow_code AS cashFlowCode, credit, debit, balance_direction AS balanceDirection,
    memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate, approve_item AS approveItem,
    approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation, apply_memo AS applyMemo,
    approve_memo AS approveMemo, message_id AS messageId
    from t_accountant_subject_cash_history
    where id = #{id}
  </select>

  <select id="listPage" parameterType="hashmap" resultType="subjectCashHistory" >
    select
    id, detail_history_id AS detailHistoryId, detail_id AS detailId, cash_flow_code AS cashFlowCode, credit, debit, balance_direction AS balanceDirection,
    memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate, approve_item AS approveItem,
    approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation, apply_memo AS applyMemo,
    approve_memo AS approveMemo, message_id AS messageId
    from t_accountant_subject_cash_history
  </select>

  <select id="getSingleByDetailID"
          resultType="subjectCashHistory">

    select
    sc.id, sc.detail_id AS detailId, sc.cash_flow_code AS cashFlowCode, sc.credit, sc.debit, sc.balance_direction AS balanceDirection,
    sc.memo, sc.creator, sc.create_name AS createName,c.name AS codeName
    from t_accountant_subject_cash_history sc,t_accountant_cashflow_category c
    where sc.detail_history_id = #{detailHistoryId} and sc.cash_flow_code=c.code

  </select>

  <delete id="delete" parameterType="subjectCashHistory" >
    delete from t_accountant_subject_cash_history
    where id = #{id}
  </delete>
  <delete id="deleteByDetailID">

            delete from t_accountant_subject_cash_history
    where detail_history_id = #{detailHistoryId}

  </delete>


  <insert id="insert" useGeneratedKeys="true" keyProperty="id" >
    insert into t_accountant_subject_cash_history (detail_history_id, detail_id,
      cash_flow_code, credit, debit, 
      balance_direction, memo, creator, 
      create_name, create_date, updator, 
      update_name, update_date, approve_item, 
      approve_status, approve_level, auditor, 
      auditor_name, audit_date, operation, 
      apply_memo, approve_memo, message_id
      )
    values (#{detailHistoryId}, #{detailId},
      #{cashFlowCode}, #{credit}, #{debit},
      #{balanceDirection}, #{memo}, #{creator},
      #{createName}, #{createDate}, #{updator},
      #{updateName}, #{updateDate}, #{approveItem},
      #{approveStatus}, #{approveLevel}, #{auditor},
      #{auditorName}, #{auditDate}, #{operation},
      #{applyMemo}, #{approveMemo}, #{messageId}
      )
  </insert>

  <update id="update" parameterType="subjectCashHistory" >
    update t_accountant_subject_cash_history
    <set >
      <if test="detailHistoryId != null" >
        detail_history_id = #{detailHistoryId},
      </if>
      <if test="detailId != null" >
        detail_id = #{detailId},
      </if>
      <if test="cashFlowCode != null" >
        cash_flow_code = #{cashFlowCode},
      </if>
      <if test="credit != null" >
        credit = #{credit},
      </if>
      <if test="debit != null" >
        debit = #{debit},
      </if>
      <if test="balanceDirection != null" >
        balance_direction = #{balanceDirection},
      </if>
      <if test="memo != null" >
        memo = #{memo},
      </if>
      <if test="creator != null" >
        creator = #{creator},
      </if>
      <if test="createName != null" >
        create_name = #{createName},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate},
      </if>
      <if test="updator != null" >
        updator = #{updator},
      </if>
      <if test="updateName != null" >
        update_name = #{updateName},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate},
      </if>
      <if test="approveItem != null" >
        approve_item = #{approveItem},
      </if>
      <if test="approveStatus != null" >
        approve_status = #{approveStatus},
      </if>
      <if test="approveLevel != null" >
        approve_level = #{approveLevel},
      </if>
      <if test="auditor != null" >
        auditor = #{auditor},
      </if>
      <if test="auditorName != null" >
        auditor_name = #{auditorName},
      </if>
      <if test="auditDate != null" >
        audit_date = #{auditDate},
      </if>
      <if test="operation != null" >
        operation = #{operation},
      </if>
      <if test="applyMemo != null" >
        apply_memo = #{applyMemo},
      </if>
      <if test="approveMemo != null" >
        approve_memo = #{approveMemo},
      </if>
      <if test="messageId != null" >
        message_id = #{messageId},
      </if>
    </set>
    where id = #{id}
  </update>

</mapper>