<template>
  <div id="complaint" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="投诉录入" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container">
      <div class="ui-cells">
        <p>  您已提交的投诉立案申请有如下{{list.length}}个</p>
        <a class="ui-cell" @click="jump(item.id)" v-for="(item, index) in list" v-bind:key="index">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div class="cusName">{{item.customName}}</div>
              <div><span class="info">接到投诉的日期</span><span class="info">{{item.receiptTime | formatDate('yyyy-MM-dd')}}</span></div>
              <div><span class="info">录入者</span><span class="info">{{item.createName}} {{item.createTime | formatDate}}</span></div>
            </div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #complaint{
    .cusName{ margin-bottom:1rem; }
    .info{margin-left:2rem; }
  }
</style>
<script>
import { formatDate } from '../../../js/common'
export default {
  name: 'complaintInput',
  data () {
    return {
      list: [],
      loading: true,
      listeners: []
    }
  },
  filters: {
    formatDate
  },
  created () {
    let _this = this
    _this.getList()
    _this.listeners.push(_this.sphdSocket.subscribe('complaintEntry', _this.complaintEntryCallBack, null, 'user'))
  },
  destroyed: function () {
    let _this = this
    _this.listeners.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    // 获取投诉录入的列表
    getList: function () {
      let _this = this
      let userID = _this.sphdSocket.user.userID
      _this.axios.post('../../../complaint/getComplaintEntry.do?userID=' + userID).then(data => {
        let res = data.data.data
        _this.list = res['listComplaint']
        _this.loading = false
      }).catch((err) => {
        console.log('获取数据错误了！', err)
      })
    },
    jump: function (id) {
      window.parent.floatToPage('../../../complaint/complaintInfo.do', { 'id': id, 't': 1 })
    },
    complaintEntryCallBack: function (res) {
      console.log('投诉录入返回值：', res)
      this.getList()
    },
    search: function () {
      this.$router.push({
        name: `applyComplaintSearch`
      })
    }
  },
  watch: {
  }
}
</script>
