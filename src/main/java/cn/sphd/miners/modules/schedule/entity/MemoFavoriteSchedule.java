package cn.sphd.miners.modules.schedule.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

// 日程收藏关联表
@Entity
@Table(name = "t_memo_favorite_schedule")
public class MemoFavoriteSchedule implements Serializable {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="memo_schedule"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer scheduleId;//日程ID

    @Column(name="memo_favorite"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer favoriteId;//收藏ID

    @Column(name="schedule_uid"  , length=36 , nullable=true , unique=false)
    private String scheduleUid;   //日程的唯一标识符。 此值用于标识分布式提醒

    @Column(name="title"  , length=255 , nullable=true , unique=false)
    private String title;   //标题

    @Column(name="is_valid"  ,  nullable=true , unique=false)
    private Boolean isValid=true;   //是否有效:移出时置为FALSE

    @Column(name="source"  , length=1 , nullable=true , unique=false)
    private String source;   //来源:1-录入,2-移入,3-复制

    @Column(name="`original`"  , nullable=true , unique=false)
    private Integer original;//'原始收藏ID,转移/复制时记录轨迹'

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_time"   , nullable=true , unique=false)
    private Date createTime;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_time"   , nullable=true , unique=false)
    private Date updateTime;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;//操作:1-增,2-删,3-改

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId;//修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo;//版本号,每次修改+1

    @JsonIgnore
    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="memo_schedule", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private MemoSchedule schedule;//日程集联

    @JsonIgnore
    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="memo_favorite", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private MemoFavorite favorite;//收藏集联


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Integer scheduleId) {
        this.scheduleId = scheduleId;
    }

    public Integer getFavoriteId() {
        return favoriteId;
    }

    public void setFavoriteId(Integer favoriteId) {
        this.favoriteId = favoriteId;
    }

    public Integer getOriginal() {
        return original;
    }

    public void setOriginal(Integer original) {
        this.original = original;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public MemoSchedule getSchedule() {
        return schedule;
    }

    public void setSchedule(MemoSchedule schedule) {
        this.schedule = schedule;
    }

    public MemoFavorite getFavorite() {
        return favorite;
    }

    public void setFavorite(MemoFavorite favorite) {
        this.favorite = favorite;
    }

    public String getScheduleUid() {
        return scheduleUid;
    }

    public void setScheduleUid(String scheduleUid) {
        this.scheduleUid = scheduleUid;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Boolean getValid() {
        return isValid;
    }

    public void setValid(Boolean valid) {
        isValid = valid;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }
}
