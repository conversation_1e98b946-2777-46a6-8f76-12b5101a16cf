<template>
  <div id="purchaseInvoiceMore">
    <TY_NavTop :title="title"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="con1">
        <div class="oInfo" >
          <p style="display: none">{{honeynew.orders.type | formatType(honeynew.ordersItem.amount) }}</p>
          <div style="display: flex;">
            <p style="margin-left: -21px;width: 175px;">
              {{honeynew.orders.supplier_name}}   <!--supplier_name-->
            </p>
            <p class="txt_right" style="width: 175px;">
              {{honeynew.orders.code_name}}
            </p>
          </div>
          <div style="display: flex" >
            <p v-if="honeynew.ordersItem.type !== '3'" style="margin-left: -21px;width: 175px;">
              订单日期&nbsp;{{honeynew.ordersItem.planDate |formatDay('YYYY-MM-DD')}}
            </p>
            <p class="txt_right"  style="width: 175px;">
              订单总额&nbsp;{{honeynew.orders.amount && honeynew.orders.amount.toFixed(2)}}
            </p>
          </div>
        </div>
        <div class="panel-content">
          <div class="text-right">
            <el-link style="color: #409eff; margin-right: 8px;" @click="openlook" v-show="conshou1">展开</el-link>
            <el-link style="color: #409eff; margin-right: 8px;" @click="openlook" v-show="conshou2">收起</el-link>
          </div>
          <div v-show="tacklook">
            <div style="display: flex" >
              <p v-if="honeynew.orders.type !== '3'" style="margin-left: -12px;">
                订单号&nbsp;{{honeynew.orders.sn}}
              </p>
            </div>
            <div style="display: flex"  class="text-right">
              <p class="txt_right"  style="margin-left: 120px;">
                录入&nbsp;{{honeynew.prepayments.createName}}&nbsp;{{honeynew.orders.create_date | formatDay('YYYY-MM-DD HH:mm:ss')}}
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="panel-content" style="margin-bottom: 12px;">
        <div>
          <table>
            <thead>
            <tr>
              <td style="text-align: center">材料</td>
              <td style="text-align: center">要求到货日期/数量</td>
            </tr>
            </thead>
            <tbody>
            <tr v-if="honeynew.ordersItem">
              <td style="text-align: center">
                {{honeynew.ordersItem.code}}{{"\xa0\xa0"}}{{honeynew.ordersItem.name}}
                {{"\xa0\xa0"}}{{honeynew.ordersItem.specifications}}{{"\xa0\xa0"}}
                {{honeynew.ordersItem.model}}
              </td>
              <td style="text-align: center;width: 211px;">
                {{honeynew.ordersItem.delivery_date |formatDay('YYYY-MM-DD')}}{{"\xa0\xa0"}}
                {{honeynew.ordersItem.quantity}}{{"\xa0\xa0"}}{{honeynew.ordersItem.unit}}
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "purchaseInvoiceMore",
  data(){
    return{
      honeynew:{
        ordersItem: {},
        orders:{
          amount:''
        },
        prepayments:{}
      },
      loading: true,
      toggle: -1,
      title: '采购预付款的申请',
      tacklook:false,
      conshou1:true,
      conshou2:false,
      orderID:0
    }
  },
  created() {
    let that = this;
    let paramCode = this.$route.params.id;
    that.orderID = paramCode;
    that.getDetail();
  },
  methods:{
    getDetail:function(){
      let that = this;
      this.axios.get('../../../po/orderDetail',{  //vue中调用get接口的格式
        params:{
          'orderId':that.orderID
        }
      })
        .then(function(response){
          let res = response.data;
          that.honeynew = {
            ordersItem: res.ordersItem[0],
            orders: res.orders[0],
            prepayments: res.prepayments[0]
          };
          that.loading = false;
        })
        .catch(function(error){
          console.log(error);
        })
    },
    openlook(){
      this.tacklook = !this.tacklook;
      this.conshou1 = !this.conshou1;
      this.conshou2 = !this.conshou2;
    }
  },
}
</script>

<style lang="less">
#purchaseInvoiceMore{
  .oInfo{ padding:5px 20px; }
  .con1{ background:#fff; margin-bottom:10px; padding:5px 15px; font-size:14px;  }
  .con1>p{text-align: right; font-size:0.8em;   }
  .right{ float: right;  }
  .ui-cell{ display: block;  }
}
</style>
