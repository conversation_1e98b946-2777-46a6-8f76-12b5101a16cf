package cn.sphd.miners.modules.accountant.controller;

import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectRelevance;
import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectRelevanceHistory;
import cn.sphd.miners.modules.accountant.service.ReimburseRelevanceService;
import cn.sphd.miners.modules.accountant.util.Result;
import cn.sphd.miners.modules.system.entity.User;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by lht on 17-1-4.
 */
@Controller
@RequestMapping("/accountant")
public class ReimburseRelevanceController {

    private static final String SUCCESS = "1";
    private static final String ERROR = "0";

    @Autowired
    ReimburseRelevanceService relevanceService;

    /*
    * 获取关联页面的数据
    * */
    @RequestMapping("/getRelevances.do")
    @ResponseBody
    public Result getRelevances(User user) {
        Result result = new Result();
//        Integer org = (Integer) session.getAttribute("oid");
        int org = user.getOid();
        Map<String,Object> res = relevanceService.getRelevances(org);
        result.setCode(SUCCESS);
        result.setMsg("");
        result.setData(res);
        return result;
    }

    /*
    * 设置关联，当关联页面点保存时触发
    * [{"feeCat":"7844","subject":"********","name":"职工薪酬","enabled":"1","code":5601,"subjectId":"99056"},{"feeCat":"9488","subject":"********","name":"职工薪酬","enabled":"1","code":5602,"subjectId":"99068"},{"feeCat":"7845","subject":"********","name":"职工薪酬","enabled":"1","code":5601,"subjectId":"99056"}]
    * */
    @RequestMapping("/setReimburseRelevance.do")
    @ResponseBody
    public Result setReimburseRelevance(User user,String list) {
        List<TAccountantSubjectRelevance> listRelevance = new ArrayList<>();
        //[{"feeCat":"7844","subject":"********","name":"职工薪酬","enabled":"1","code":5601,"subjectId":"99056"},
        // {"feeCat":"9488","subject":"********","name":"职工薪酬","enabled":"1","code":5602,"subjectId":"99068"},
        // {"feeCat":"7845","subject":"********","name":"职工薪酬","enabled":"1","code":5601,"subjectId":"99056"}]
        JSONArray array = new JSONArray(list);
        for (int i=0;i<array.length();i++) {
            JSONObject item = array.getJSONObject(i);
            int feeCat = item.optInt("feeCat");
            int enabled = item.optInt("enabled");
            int subjectId = item.optInt("subjectId");
            String subject = item.optString("subject");
            String name = item.optString("name");
            String code = item.optString("code");
            TAccountantSubjectRelevance relevance = new TAccountantSubjectRelevance();
            relevance.setFeeCat(feeCat);
            relevance.setCode(code);
            relevance.setEnabled(enabled);
            relevance.setSubject(subject);
            relevance.setName(name);
            relevance.setSubjectId(subjectId);
            listRelevance.add(relevance);
        }

        Result result = new Result();
//        User user = (User) session.getAttribute("user");
        int res = relevanceService.setReimburseRelevance(user,listRelevance);
        result.setCode(SUCCESS);
        result.setMsg("");
        result.setData(res);
        return result;
    }

    /*
    * 返回某高级科目的历史关联设置列表
    * */
    @RequestMapping("/getCategoryRelevanceHistory.do")
    @ResponseBody
    public Result getCategoryRelevanceHistory(User user,TAccountantSubjectRelevanceHistory history) {
        Result result = new Result();
//        Integer org = (Integer) session.getAttribute("oid");
        int org = user.getOid();
//        TAccountantSubjectRelevanceHistory param = new TAccountantSubjectRelevanceHistory();
//        param.setCode(code);
//        param.setOrg(org);
        history.setOrg(org);
        List<TAccountantSubjectRelevanceHistory> res = relevanceService.getCategoryRelevanceHistory(history);
        result.setCode(SUCCESS);
        result.setMsg("");
        result.setData(res);
        return result;
    }


}
