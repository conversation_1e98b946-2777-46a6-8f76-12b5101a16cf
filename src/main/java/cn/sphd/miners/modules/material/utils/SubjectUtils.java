package cn.sphd.miners.modules.material.utils;

import cn.sphd.miners.modules.accountant.service.SubjectSelectService;
import cn.sphd.miners.modules.accountant.service.SubjectSettingService;
import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.entity.SlCustomer;
import cn.sphd.miners.modules.commodity.service.ProductService;
import cn.sphd.miners.modules.material.entity.MtBase;
import cn.sphd.miners.modules.material.entity.MtCategory;
import cn.sphd.miners.modules.material.entity.SrmSupplier;
import cn.sphd.miners.modules.personal.entity.UserMessage;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

public class SubjectUtils {


    public SubjectUtils() {

    }

    public void addSubject(User user, List<SrmSupplier> suppliers, MtCategory mtCategory, String subject,
                           SubjectSelectService subjectSelectService, SubjectSettingService settingService, Integer oid,
                           MtBase mtBase, PdBase pdBase, UserMessageService userMessageService, UserService userService, SlCustomer slCustomer, ProductService productService) {
        int res = subjectSelectService.getRelation(user);


        String list = "[]";
        JSONArray listArray;
        String newSubject = "";
        JSONObject yos;
        JSONObject result;

        if(mtBase!=null){
            if(StringUtils.isBlank(mtBase.getName()))
                mtBase.setName(mtBase.getProduct().getName());
            if(StringUtils.isBlank(mtBase.getCode()))
                mtBase.setCode(mtBase.getProduct().getInnerSn());
        }
        //设置了关联begin
        if (res == 1) {

            if (pdBase != null) {//新增了商品
                list = settingService.firstSubject("1405", oid);
            } else if (mtCategory.getName().equals("构成商品的原辅材料") || mtCategory.getName().equals("外购成品")) {
                list = settingService.firstSubject("1403", oid);

            } else if (mtCategory.getName().equals("办公用品")) {
                list = settingService.firstSubject("********", oid);

            } else if (mtCategory.getName().equals("商品的包装物")) {
                list = settingService.firstSubject("1412", oid);
            } else {
                for (SrmSupplier supplier : suppliers) {
                        User accounting = null;
                        accounting = userService.getUserByRoleCode(oid, "accounting");
                        if (accounting == null) {
                            accounting = userService.getUserByRoleCode(oid, "agentAccounting");
                        }
                        if (accounting != null) {
                            UserMessage userMessage = new UserMessage();
                            userMessage.setUser(user);
                            userMessage.setApprovalStatus(1);//审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核
                            userMessage.setHandleId(user.getUserID().toString());
                            userMessage.setEventType("科目设置");
                            userMessage.setIllustrate(mtBase.getName() + mtBase.getCode() + supplier.getName() + "的科目设置");//操作说明（申请事件）
                            userMessage.setMessageType("11");////消息类型  1-财务  2-加班  3-请假   4-报销  5-仓库修正 6-出库
                            userMessage.setState(1);//1-未处理  2-已处理
                            userMessage.setReceiveUserId(accounting.getUserID());  //接收消息人
                            userMessage.setCreateDate(new Date());
                            userMessage.setMtStockId(mtBase.getId());
                            userMessage.setMessageId(supplier.getId());
                            userMessageService.addUserMassage(userMessage);
                        }

                }
                return;
            }


            if (pdBase != null) {
                List<PdMerchandise> pdMerchandises = productService.getByINnerSn(pdBase.getInnerSn(),pdBase.getId());


                for(PdMerchandise pdMerchandise:pdMerchandises){
                    list = settingService.firstSubject("1405", oid);
                    listArray = JSONArray.fromObject(list);
                    for (int z = 0; z < JSONArray.toList(listArray).size(); z++) {
                        yos = JSONObject.fromObject(listArray.get(z));

                        newSubject = yos.optString("newSubject");

                        String sb = this.check(yos.optString("subject"), slCustomer.getName(), slCustomer.getId(),settingService);
                        if ("".equals(sb)) {//没有重复的
                            result = settingService.newAccountantSubject(yos.optString("subject"), oid, slCustomer.getName(),
                                    null, user.getUserID(), user.getUserName(), newSubject,
                                    "1", "4", slCustomer.getId());

                            list = settingService.firstSubject(newSubject, oid);

                            if(list!=null){
                                listArray = JSONArray.fromObject(list);

                                for (int x = 0; x < JSONArray.toList(listArray).size(); x++) {

                                    yos = JSONObject.fromObject(listArray.get(z));
                                    result = settingService.newAccountantSubject(yos.optString("subject"), oid, pdMerchandise.getOuterName() + pdMerchandise.getOuterSn() + slCustomer.getName(),
                                            null, user.getUserID(), user.getUserName(), yos.optString("newSubject"), "1", "5", pdBase.getId());
                                }
                            }


                        } else {

                            list = settingService.firstSubject(sb, oid);
                            listArray = JSONArray.fromObject(list);

                            for (int x = 0; x < JSONArray.toList(listArray).size(); x++) {

                                yos = JSONObject.fromObject(listArray.get(z));
                                result = settingService.newAccountantSubject(yos.optString("subject"), oid, pdMerchandise.getOuterName() + pdMerchandise.getOuterSn() + slCustomer.getName(),
                                        null, user.getUserID(), user.getUserName(), yos.optString("newSubject"), "1", "5", pdBase.getId());

                            }
                        }
                    }
                }


                return;
            }

            if (suppliers.size() > 0) {

                for (int i = 0; i < suppliers.size(); i++) {

                    listArray = JSONArray.fromObject(list);
                    if (i > 0) listArray = JSONArray.fromObject(settingService.firstSubject(subject, oid));
                    for (int z = 0; z < JSONArray.toList(listArray).size(); z++) {
                        yos = JSONObject.fromObject(listArray.get(z));

                        newSubject = yos.optString("newSubject");
                        subject = yos.optString("subject");

                        String sb = this.check(yos.optString("subject"), suppliers.get(i).getName(),suppliers.get(i).getId(), settingService);

                        if ("".equals(sb)) {
                            result = settingService.newAccountantSubject(yos.optString("subject"), oid, suppliers.get(i).getName(),
                                    null, user.getUserID(), user.getUserName(), yos.optString("newSubject"),
                                    "1", "3", suppliers.get(i).getId());
                            list = settingService.firstSubject(newSubject, oid);

                            if (list != null) {
                                listArray = JSONArray.fromObject(list);

                                for (int x = 0; x < JSONArray.toList(listArray).size(); x++) {

                                    yos = JSONObject.fromObject(listArray.get(z));
                                    result = settingService.newAccountantSubject(yos.optString("subject"), oid, mtBase.getName() + mtBase.getCode() + suppliers.get(i).getName(),
                                            mtBase.getUnit(), user.getUserID(), user.getUserName(), yos.optString("newSubject"), "1", "2", mtBase.getId());

                                }

                            }

                        } else {

                            list = settingService.firstSubject(sb, oid);
                            listArray = JSONArray.fromObject(list);

                            for (int x = 0; x < JSONArray.toList(listArray).size(); x++) {

                                yos = JSONObject.fromObject(listArray.get(z));
                                result = settingService.newAccountantSubject(yos.optString("subject"), oid, mtBase.getName() + mtBase.getCode() + suppliers.get(i).getName(),
                                        mtBase.getUnit(), user.getUserID(), user.getUserName(), yos.optString("newSubject"), "1", "2", mtBase.getId());

                            }
                        }

                    }

                }

            }

        }
    }

    public void updateCustomerProduct(PdMerchandise pdMerchandise, JSONObject newPd, SubjectSettingService settingService, Integer oid, User user, SubjectSelectService subjectSelectService) {
        int res = subjectSelectService.getRelation(user);


        if (res == 1) {
            String list = settingService.firstSubject("1405", oid);
            JSONArray listArray;
            JSONObject yos;
            String newSubject;
            //情况1：供应商没改， 外部名称、外部图号 改了，，那么就在原来供应商的科目下面生成新的科目， 老的不管

//            if((!newPd.optString("outerSn").equals(pdMerchandise.getOuterSn())||!newPd.optString("outerName").equals(pdMerchandise.getOuterName()))
//                    &&newPd.optInt("customerId")==pdMerchandise.getCustomer().getId()){

            listArray = JSONArray.fromObject(list);
            for (int z = 0; z < JSONArray.toList(listArray).size(); z++) {
                yos = JSONObject.fromObject(listArray.get(z));

                newSubject = yos.optString("newSubject");


                JSONObject result = settingService.newAccountantSubject(yos.optString("subject"), oid, newPd.optString("customerName"),
                        null, user.getUserID(), user.getUserName(), newSubject,
                        "1", "3", pdMerchandise.getId());

                if (result != null) {
                    if (!"".equals(result.optString("existSubject"))) {//新增失败，有重复的,则在已有供应商下新增
                        list = settingService.firstSubject(result.optString("existSubject"), oid);
                        listArray = JSONArray.fromObject(list);

                        for (int x = 0; x < JSONArray.toList(listArray).size(); x++) {

                            yos = JSONObject.fromObject(listArray.get(z));
                            result = settingService.newAccountantSubject(yos.optString("subject"), oid, newPd.optString("outerName") + newPd.optString("outerSn") + newPd.optString("customerName"),
                                    null, user.getUserID(), user.getUserName(), yos.optString("newSubject"), "1", "5", pdMerchandise.getId());

                        }
                        return;
                    } else {//新增成功了 新增成功的subject为  newSubject
                        list = settingService.firstSubject(newSubject, oid);
                        listArray = JSONArray.fromObject(list);
                        for (int u = 0; u < JSONArray.toList(listArray).size(); u++) {
                            yos = JSONObject.fromObject(listArray.get(u));

                            result = settingService.newAccountantSubject(newSubject, oid, newPd.optString("outerName") + newPd.optString("outerSn") + newPd.optString("customerName"),
                                    null, user.getUserID(), user.getUserName(), yos.optString("newSubject"), "1", "5", pdMerchandise.getId());

                        }
                    }
                }
            }
        }
    }


    public void updateCustomerNameOrCode(List<PdMerchandise> pdMerchandises, SubjectSettingService settingService, Integer oid, User user, SubjectSelectService subjectSelectService) {
        int res = subjectSelectService.getRelation(user);


        if (res == 1) {
            String list = settingService.firstSubject("1405", oid);
            JSONArray listArray;
            JSONObject yos;
            String newSubject;

            listArray = JSONArray.fromObject(list);

            for(PdMerchandise pdMerchandise : pdMerchandises){
                for (int z = 0; z < JSONArray.toList(listArray).size(); z++) {
                    yos = JSONObject.fromObject(listArray.get(z));

                    newSubject = yos.optString("newSubject");


                    JSONObject result = settingService.newAccountantSubject(yos.optString("subject"), oid, pdMerchandise.getCustomerName(),
                            null, user.getUserID(), user.getUserName(), newSubject,
                            "1", "3", pdMerchandise.getId());

                    if (result != null) {
                        if (!"".equals(result.optString("existSubject"))) {//新增失败，有重复的,则在已有供应商下新增
                            list = settingService.firstSubject(result.optString("existSubject"), oid);
                            listArray = JSONArray.fromObject(list);

                            for (int x = 0; x < JSONArray.toList(listArray).size(); x++) {

                                yos = JSONObject.fromObject(listArray.get(z));
                                result = settingService.newAccountantSubject(yos.optString("subject"), oid, pdMerchandise.getOuterName() + pdMerchandise.getOuterSn() + pdMerchandise.getCustomerName(),
                                        null, user.getUserID(), user.getUserName(), yos.optString("newSubject"), "1", "5", pdMerchandise.getId());

                            }
                            return;
                        } else {//新增成功了 新增成功的subject为  newSubject
                            list = settingService.firstSubject(newSubject, oid);
                            listArray = JSONArray.fromObject(list);
                            for (int u = 0; u < JSONArray.toList(listArray).size(); u++) {
                                yos = JSONObject.fromObject(listArray.get(u));

                                result = settingService.newAccountantSubject(newSubject, oid, pdMerchandise.getOuterName() + pdMerchandise.getOuterSn() + pdMerchandise.getCustomerName(),
                                        null, user.getUserID(), user.getUserName(), yos.optString("newSubject"), "1", "5", pdMerchandise.getId());

                            }
                        }
                    }
                }
            }

        }
    }
    //查父类下有没有重复，若已有 则返回此项编号
    public String check(String subject, String name,Integer id, SubjectSettingService subjectSettingService) {
        return subjectSettingService.checkRepeat(subject, name,id);
    }

    /**
     * @param oldId 老的高管id  id：新高管id
     * */

    public void sendMessages(Integer oldId,Integer id,UserMessageService userMessageService){
        List<UserMessage> userMessages = userMessageService.findUserMessageByUserIdAndStateAndMessageState(oldId,1,11);

        if(userMessages!=null&&userMessages.size()>0)

            for(UserMessage userMessage:userMessages){
                userMessage.setReceiveUserId(id);
                userMessageService.updateUserMassage(userMessage);
            }
    }
}

