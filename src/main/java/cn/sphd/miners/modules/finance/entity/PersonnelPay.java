package cn.sphd.miners.modules.finance.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by lyx on 2022/4/19.
 * 人事_员工薪酬表  1.206工资之支付(t_personnel_salary表作废)
 */
@Entity
@Table(name="t_personnel_pay")
public class PersonnelPay implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;   //机构ID

    @Column(name="user"   , nullable=true , unique=false)
    private Integer user;   //用户ID

    @Column(name="offer"   , nullable=true , unique=false)
    private Integer offer;   //招聘ID

    @Column(name="period"  , length=6 , nullable=true , unique=false)
    private String period;  //所属月份

    @Column(name="type"   , nullable=true , unique=false)
    private Byte type;  //类型:1-工资,2-所得税,3-社保,4-公积金

    @Column(name="pay"  , nullable=true , unique=false)
    private BigDecimal pay;   //应付金额(实发总额)

    @Column(name="fact_pay"  , nullable=true , unique=false)
    private BigDecimal factPay;   //实付金额

    @Column(name="pay_time"  , nullable=true , unique=false)
    private Date payTime;   //发放时间

    @Column(name="account_detail", nullable=true , unique=false)
    private Integer accountDetail;   //财务帐务明细ID

    @Column(name="pay_way"   , nullable=true , unique=false)
    private Byte payWay;  //发放方式:1-现金,2-转帐

    @Column(name="finance_account",nullable=true , unique=false)
    private Integer financeAccount;  //银行账户id

    @Column(name="pay_account"  , length=50 , nullable=true , unique=false)
    private String payAccount;  //转帐帐号

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;  //操作:1-增,2-删,3-改,4-调财务数据,5-修改发放信息,6-修改工资数,7-修改支出信息,8-修改职工项

    @Column(name="previous_id"  , nullable=true , unique=false)
    private Integer previousId;  //修改前记录ID

    @Column(name="version_no"  , nullable=true , unique=false)
    private Integer versionNo;  //版本号(修改批次)

    @Column(name="sub_version_no"  , nullable=true , unique=false)
    private Integer subVersionNo;  //子版本号(修改记录)

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getUser() {
        return user;
    }

    public void setUser(Integer user) {
        this.user = user;
    }

    public Integer getOffer() {
        return offer;
    }

    public void setOffer(Integer offer) {
        this.offer = offer;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public BigDecimal getPay() {
        return pay;
    }

    public void setPay(BigDecimal pay) {
        this.pay = pay;
    }

    public BigDecimal getFactPay() {
        return factPay;
    }

    public void setFactPay(BigDecimal factPay) {
        this.factPay = factPay;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Integer getAccountDetail() {
        return accountDetail;
    }

    public void setAccountDetail(Integer accountDetail) {
        this.accountDetail = accountDetail;
    }

    public Byte getPayWay() {
        return payWay;
    }

    public void setPayWay(Byte payWay) {
        this.payWay = payWay;
    }

    public Integer getFinanceAccount() {
        return financeAccount;
    }

    public void setFinanceAccount(Integer financeAccount) {
        this.financeAccount = financeAccount;
    }

    public String getPayAccount() {
        return payAccount;
    }

    public void setPayAccount(String payAccount) {
        this.payAccount = payAccount;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Integer getSubVersionNo() {
        return subVersionNo;
    }

    public void setSubVersionNo(Integer subVersionNo) {
        this.subVersionNo = subVersionNo;
    }
}
