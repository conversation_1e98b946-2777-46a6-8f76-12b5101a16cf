<template>
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showClose=true :showBack=true @mycloseFun="handleCustomClose">
        <p v-if="billStatistics.state==4" @click="statistics()">统计</p>
      </PageHeader>
    </div>
    <div class="sticky ty-fff" v-if="billStatistics">
      <p class="padding-0-15">{{ new
      Date(list.beginDate).format('yyyy-MM-dd') }}至{{ new
      Date(list.endDate).format('yyyy-MM-dd') }}期间收到的外部转账支票中</p>
      <div class="padding-0-15" v-if="billStatistics&&list.numAmount">
        <span v-if="billStatistics.state==1">还在手中</span><span v-else-if="billStatistics.state==2">已存入银行</span><span
          v-else-if="billStatistics.state==4">又付出去</span>的共{{ list.numAmount.num
        }}张，{{ list.numAmount.amount == null ? 0 : this.formatCurrency(list.numAmount.amount) }}元，具体如下：
      </div>
    </div>
    <div class="re margin_top_16" v-if="list.financeReturns">
      <div class="spaceBetween" v-for="(item,index) in list.financeReturns" :key="index"
           :class="{ 'f4': index % 2 !== 0 ,'ty-fff':index%2===0}">
        <div>
          <p>
            <span>到期日<span class="padding-0-5">{{ new
            Date(item.expireDate).format('yyyy-MM-dd') }}</span></span>
            <span class="padding-0-10">票据金额<span class="padding-0-5">{{ this.formatCurrency(item.amount)
              }}</span></span>
          </p>
          <p>支票号<span class="padding-0-5">{{ item.returnNo }}</span></p>
          <p>录入者<span class="padding-0-5">{{ item.createName }}</span><span class="padding-0-5">{{ new
          Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span><span class="padding-0-10">{{ item.inputDate
            }}</span>
          </p>
        </div>
        <div class="ic" @click="rou(item.id)">
          <el-icon style="font-size: 20px">
            <arrow-right/>
          </el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getReturnList} from '@/api/financeBill'
import {formatCurrency} from '@/utils/utils'

export default {
  name: "cmoStatisticsDetails",
  data() {
    return {
      tit: "财会",
      formatCurrency: formatCurrency,
      billStatistics: {},
      list: []
    }
  },
  computed: {},
  methods: {
    statistics() {
      let payStatisticsList = {beginTime: this.list.beginDate}
      localStorage.setItem('payStatisticsList', JSON.stringify(payStatisticsList))
      this.$router.push({path: "/cmoPayStatisticsList"})
    },
    handleCustomClose() {
      this.$router.push("/caikuai")
    },
    getList(billStatistics) {
      const params = {
        type: 1,
        state: billStatistics.state,
        beginTime: new Date(billStatistics.beginTime).format('yyyy-MM-dd')
      }
      getReturnList(params).then(res1 => {
        this.list = res1.data.data
      })
    },
    rou(billId) {
      this.$router.push({path: "/moneyOrderDetail", query: {billId: billId}})
    },
  },
  mounted() {
    let billStatistics = localStorage.getItem('billStatistics');
    this.billStatistics = JSON.parse(billStatistics);
    this.getList(this.billStatistics)
  },
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

.head {
  position: fixed;
  width: 100%;
  z-index: 999;
}

.sticky {
  padding: 10px 0 10px 0;
  line-height: 20px;

  > div {
    font-size: 14px;
  }
}

.re {
  position: relative;
  top: 40px;

  p {
    line-height: 20px;
  }

  .spaceBetween {
    > div {
      padding: 10px 15px;
    }
  }
}
</style>