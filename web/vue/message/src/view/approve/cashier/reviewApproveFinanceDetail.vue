<template>
  <div id="reviewApproveFinanceDetail"  v-loading.fullscreen.lock="loading">
    <TY_NavTop title="付款复核"></TY_NavTop>
    <div class="detailInfo" >
      <div style="padding: 20px 0 0 20px">
        <div class="flexC">
          <span><span >应付款金额 </span><span> {{personnelReimburse.amount}} 元 </span></span>
          <span @click="jumpDetail()" class="btncat">票据详情</span>
        </div>
        <div>
          <span class="mar30">收款人</span><span>  {{personnelReimburse.createName}}</span>
        </div>
      </div>
      <div class="panel-content">
        <div class="panel-content">
          <div class="text-right" v-on:click="toggle = -toggle"><el-link>审批记录<i :class="toggle === -1?'el-icon-arrow-down':'el-icon-arrow-up'"></i> </el-link></div>
          <div v-show="toggle > 0">
            <div class="item-content">
              <div class="processItem">
                <div>提交报销申请</div>
                <div>申请人</div>
                <div>{{personnelReimburse.createName | stringSplit(4)}} {{personnelReimburse.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
              <div v-for="it in approvalProcessList" v-bind:key="it.id">
                <div v-if="it.approveStatus === '2' && !it.businessType" class="processItem" >
                  <div>在线审批通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="nowApproveItem.approveStatus !== '5'">
                  <div v-if="it.approveStatus === '7'" class="processItem">
                    <div>票据审核通过</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 17" class="processItem">
                    <div>付款审批通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 21" class="processItem">
                    <div>付款方式确认</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 20" class="processItem">
                    <div>付款复核通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 22" class="processItem">
                    <div>付款方式修改</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 32" class="processItem">
                    <div>在线审核通过</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 33" class="processItem">
                    <div>线下审核通过</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '5'" class="processItem">
                    <div>
                      <span v-if="financeAccount.accountType == '2'">报销款已转账</span>
                      <span v-if="financeAccount.accountType == '1'">现金付讫</span>
                    </div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                </div>
              </div>
              <!-- 最后一条审批记录展示 -->
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType===32">
                <div style="margin-top:15px;"> 票据在线审核未通过！</div>
                <div><span style="float: left">理由：</span> <div style="margin-left: 50px;">{{ showReason }}</div></div>
                <div class="processItem">
                  <div></div>
                  <div>审批人</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && !nowApproveItem.businessType">
                <div style="margin-top:15px;">报销申请被驳回！</div>
                <div>驳回理由： {{nowApproveItem.reason || ''}}</div>
                <div class="processItem">
                  <div></div>
                  <div>审批人</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType===33">
                <div style="margin-top:15px;">  线下审核未通过！</div>
                <div>驳回理由: 实际票据与在线审核通过的不一致。</div>
                <div class="processItem">
                  <div></div>
                  <div>出纳员</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '8'">
                <div style="margin-top:15px;">被驳回！</div>
                <div>驳回理由： {{nowApproveItem.reason || ''}}</div>
                <div class="processItem">
                  <div></div>
                  <div>出纳员</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-blue">
                <span v-if="nowApproveItem.approveStatus === '1' && !isNowApprover && nowApproveItem.businessType===32">等待出纳在线审核。</span>
                <span v-if="nowApproveItem.approveStatus === '1' && !isNowApprover && nowApproveItem.businessType===33">
                请将实际票据交出纳，由出纳进行线下审核。<br>
                注意：实际票据需与在线审核通过的票据一致！</span>
                <span v-if="nowApproveItem.approveStatus === '1' && isNowApprover && nowApproveItem.businessType===33">
                请等待报销者提交实际票据。<br>
                注意：实际票据需与在线审核通过的票据一致！</span>
                <span v-if="nowApproveItem.approveStatus === '5'">报销申请在线审批已完成。</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div>
      <div class="handle_button">
        <span @click="jumpFu3(id , param)" class="btncat" style="margin-right:50px; font-size:1.2em; ">原定的付款方式</span>
      </div>
      <el-form :label-position="left" :rules="rules" label-width="110px" :model="square" ref="square">
        <el-form-item label="计划付款方式"><span>{{payInfo.method == '1' ? '现金 ': '银行转账' }}</span></el-form-item>
        <el-form-item label="计划付款日期"><span>{{payInfo.planDate | formatDay('YYYY-MM-DD') }}</span></el-form-item>
        <el-form-item label="账户名称"><span>{{selectFin.name }}</span></el-form-item>
        <el-form-item label="账户类型"><span>{{selectFin.isPublic == '1' ? '对公户 ': '非对公户' }} </span></el-form-item>
        <el-form-item label="开户行"><span>{{selectFin.bankName}}</span></el-form-item>
        <el-form-item label="账号"><span>{{selectFin.account}}</span></el-form-item>
        <el-form-item label="账户余额"><span>{{selectFin.balance.toFixed(2)}}</span></el-form-item>
        <div v-if="nowApproveItem.approveStatus === '1' && nowApproveItem.businessType == 20">
          <div v-if="selectFin.isPublic != '1'">
            <p class="orangeTip" >请告知出纳您所收到的手机动态密码！</p>
            <p style="padding-left: 100px;margin-top:20px;">
              <el-checkbox label="出纳已获知手机动态密码！" name="type" @change="handleChange()"></el-checkbox>
            </p>
          </div>
          <div v-if="selectFin.isPublic == '1'">
            <p class="orangeTip"  >请授权出纳使用银行提供的U盾、<br/>
              口令卡或其他数字安全设备！</p>
            <p style="padding-left:100px;margin-top:20px;">
              <el-checkbox label="已向出纳授权！" name="type" @change="handleChange()"></el-checkbox>
            </p>
          </div>
          <div class="handle_button" style="text-align: center;">
            <span style="width:80%; " class="ui-btn ui-btn_info"  @click="approve()">确 定</span>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>
<style lang="less">
#reviewApproveFinanceDetail{
  overflow: auto!important;
  .processItem{
    display: flex;
    font-size: 11px;
    margin-left:-5px;
    div:nth-child(1){ text-align: left; flex: 1 }
    div:nth-child(2){ text-align: center; flex: 1 }
    div:nth-child(3) { text-align: right; flex: 2 }
  }
  .el-form-item__label, .el-form-item__content{
    line-height:25px;
  }
  .orangeTip{
    text-align: center; margin-top:20px; color:#ff9304; font-size:14px; font-weight:bold ;
  }
  .tipBlue{
    color:#0a4d85; text-align: center;
  }
  .detailInfo{
    background:#fff; font-size:14px;
    border-bottom:1px solid #ddd ;
  }
  .mar30{ margin-right:30px;  }
  .btncat{ color:#0b9df9; font-size:1.1em; cursor: default;  }
  .btncat:hover{ color: #0883f9; text-decoration:underline;font-weight:bold;   }
  .flexC{
    display: flex;margin-bottom:15px ;
    &>span{ display:block; flex: 1;  }
  }
}
</style>
<script>
var that
export default {
  name: 'reviewApproveFinanceDetail',
  data () {
    return {
      loading: true,
      updatePay: false,
      toggle: -1,
      balance: 0,
      selectFin: {
        'name': '',
        'balance': 0,
        'isPublic': 0,
        'bankName': '',
        'account': ''
      },
      payInfo: {
        'method': 0,
        'planDate': 0
      },
      personnelReimburse: {},
      nowApproveItem: {},
      approvalProcessList: {},
      financeAccounts: {},
      isNowApprover: false
    }
  },
  created () {
    that = this
    that.id = this.$route.params.id
    that.param = that.$route.params.billCat
    that.reimburseId = that.id
    that.listenersUid = [
      that.sphdSocket.subscribe('paymentReviewMessage', function (data) {
        that.loading = false
        let info = JSON.parse(data)
        let content = info.content
        console.log(info)
        that.$kiko_message(content)
        that.$router.push({
          path: `/reviewFinanceReimburse`
        })
      }),
      that.sphdSocket.subscribe('paymentReviewHandle', function (data) {
        console.log('待复核审批 订阅:' + data)
        let getData = JSON.parse(data)
        let personnelReimburse = getData.personnelReimburse
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.financeReview.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          if (that.reimburseId === reimburseId) {
            this.getDetails(that.reimburseId,)
          }
        }
      }, null, 'user')
    ]
    this.getDetails(that.reimburseId)


  },
  destroyed: function () {
    that.listenersUid.forEach(function (item) {
      that.sphdSocket.unsubscribe(item)
    })
  },
  filters: {
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  methods: {
    getDetails: function (reimburseId) {
      let that = this
      let params = {
        reimburseId:reimburseId
      }
      this.axios.post('../../../reimburseWindow/getReimburseDetail.do', params)
        .then(function (response) {
          console.log('详情=', response)
          let data = response.data.data
          console.log('详情 data=', data)
          that.loading = false
          let ReimburseDetail = data

          // 报销申请信息
          that.personnelReimburse = ReimburseDetail.personnelReimburse
          // 审批列表
          that.approvalProcessList = ReimburseDetail.approvalProcessList
          that.nowApproveItem = ReimburseDetail.approvalProcessList[ReimburseDetail.approvalProcessList.length - 1]
          if (ReimburseDetail.approvalProcessList.length > 0) {
            let lastToUser = ReimburseDetail.approvalProcessList[ReimburseDetail.approvalProcessList.length - 1].toUser
            if (!lastToUser || lastToUser === that.sphdSocket.user.userID) {
              that.isNowApprover = true
            }
          }
          // 付款方式信息
          that.payInfo = ReimburseDetail.personnelReimbursePayment
          // 账户信息
          that.selectFin = ReimburseDetail.financeAccount


        })
        .catch(function (error) {
          console.log(error)
        })
    },


    handleChange: function () {
      that.updatePay = !that.updatePay
    },
    approve: function () {
      if (that.updatePay) {
        let approvalProcessId = that.approvalProcessList[that.approvalProcessList.length - 1]['id']
        this.sphdSocket.send('reviewApproval', { userId: that.sphdSocket.user.userID, session: that.sphdSocket.sessionid, approvalProcessId: approvalProcessId })
      } else {
        that.$kiko_message('需要先勾选确认！')
      }
    },
    jumpFu3: function (id, param) {
      let json = {
        reimburseId: that.reimburseId
      }
      that.axios.post('../../../paymentApproval/getPaymentMethod.do', json).then(function (response) {
        let res = response.data
        console.log(res)
        that.loading = false
        // 原付款方式信息
        let payInfoOld = res.data.personnelReimbursePayment
        // 账户信息
        // let selectFinOld = res.data.financeAccount
        if (payInfoOld.method) {
          that.$router.push({
            path: `/approveReimburseDetailFu3/${id}/${param}`
          })
        } else {
          that.$kiko_message('原计划的付款方式没有修改过')
        }
      }).catch(function (error) {
        console.log(error)
      })
    },
    jumpDetail: function () {
      let id = '00' + that.id
      that.$router.push({
        path: `/approveReimburseDetail/${id}/0`
      })
    }
  }
}
</script>
