package cn.sphd.miners.modules.inStockCheck.service;

import cn.sphd.miners.modules.inStockCheck.entity.TMtInboundItemPackaging;

import java.util.List;
public interface TMtInboundItemPackagingService{

    int deleteByPrimaryKey(Integer id);

    int deleteByPrimaryKeyIn(List<Integer> list);

    int insert(TMtInboundItemPackaging record);

    int insertOrUpdate(TMtInboundItemPackaging record);

    int insertOrUpdateSelective(TMtInboundItemPackaging record);

    int insertSelective(TMtInboundItemPackaging record);

    TMtInboundItemPackaging selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TMtInboundItemPackaging record);

    int updateByPrimaryKey(TMtInboundItemPackaging record);

    int updateBatch(List<TMtInboundItemPackaging> list);

    int updateBatchUseMultiQuery(List<TMtInboundItemPackaging> list);

    int updateBatchSelective(List<TMtInboundItemPackaging> list);

    int batchInsert(List<TMtInboundItemPackaging> list);

    int batchInsertSelectiveUseDefaultForNull(List<TMtInboundItemPackaging> list);



	List<TMtInboundItemPackaging> findByAll(TMtInboundItemPackaging tMtInboundItemPackaging);


}
