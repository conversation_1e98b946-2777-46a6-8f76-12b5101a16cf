package cn.sphd.miners.modules.thirdPlatform.service;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.modules.ec.entity.EcOrder;
import cn.sphd.miners.modules.thirdPlatform.entity.TpApp;
import cn.sphd.miners.modules.thirdPlatform.entity.TpMember;
import cn.sphd.miners.modules.thirdPlatform.entity.TpMerchant;
import cn.sphd.miners.modules.thirdPlatform.entity.TpPlatform;
import org.apache.commons.lang3.tuple.Pair;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022年06月07日 15:03
 **/
public interface TgService {
    void getTpAccessTokenToRedis(TpPlatform platform);
    void getAppAccessTokenToRedis(TpApp app);
    void receiveAuthEvent(String postData, String signature, String timeStamp, String nonce, String encryptType, String msgSignature);
    void savePayNotify(Map<String, Object> params);
    Pair<TpMember, MyException> code2Member(TpApp app, String code, Map<String, String> userInfo);
    void initMerchantService(TpMerchant merchant);
    JsonResult preOrder(HttpServletRequest request, EcOrder order);
    JsonResult refundOrder(HttpServletRequest request, EcOrder order);
}
