
import {request} from "@/utils/request";

/*
 * recode: 2024-7-11  hxz
 *   creator: sy 2023-05-11   工序设置首页
 * params:
*/
export function getProcessSettingsList() {
    return request({
        url: '/processSettings/getProcessSettingsList.do',
    })
}

/*
*   create: sy 2024-11-15   工序设置列表修改是否带有子工序的状态
 */
export function upProcesSettings(){
    return request({
        url:"/processSettings/upProcessSettingSonState.do",
    })
}



/*
 * recode: 2024-7-11  hxz
 *   creator: sy 2023-05-11    展示去管理获取的列表
 * params:
*/
export function getToManageList(data) {
    return request({
        url: '/processSettings/getToManageList.do',
        data
    })
}

// /*
// *   creator: sy 2024-07-24  展示去管理获取的列表
//  */
// export function getToManageList(data){
//     data = data || {};
//     return request({
//         url: '/processSettings/getToManageList.do',
//         method: 'post',
//         data: data
//     })
// }


/*
 * recode: 2024-7-11  hxz
 *    creator: sy 2023-05-11   展示‘查看设置’页面
 * params:
*/
export function getSettingsList(data) {
    return request({
        url: '/processSettings/getSettingsList.do',
        data
    })
}



/*
 * recode: 2024-7-11  hxz
 *    creator: sy 2023-05-11   工序列表
 * params:
*/
export function getProcessList(data) {
    return request({
        url: '/processSettings/getProcessList.do',
        data
    })
}


/*
 * recode: 2024-7-11  hxz
 *    creator: sy 2023-05-11   类别 列表
 * params:
 *  Integer type：类型:1-工序,2-子工序,3-操作,4-套餐
	Integer enabled：1-启用的，0-停用的
*/
export function getCategoryList(data) {
    return request({
        url: '/processSettings/getCategoryList.do',
        data
    })
}


/*
 * recode: 2024-7-11  hxz
 *    creator: sy 2023-05-11   类别 列表
 * params:
 *  Integer product：产品id
	Integer categoryId：类别id
*/
export function getSonProcessList(data) {
    return request({
        url: '/processSettings/getSonProcessList.do',
        data
    })
}


/*
 * recode: 2024-7-11  hxz
 * params:
 *  Integer categoryId：类别id
*/
export function getOperateList(data) {
    return request({
        url: '/processSettings/getOperateList.do',
        data
    })
}


/*  创建工序选项
 * recode: 2024-7-11  hxz
 * params:
 *  String code：代号
	String name：名称
	Integer category：类别id
*/
export function createProcess(data) {
    return request({
        url: '/processSettings/createProcess.do',
        data
    })
}



/*
 * recode: 2024-7-11  hxz
 * 进行设置操作**
 * params:
    Integer product：产品id
	String processSJson：设置的内容列表{
		processId：工序id
		order：排序
		sonProcessHaving：1-有子工序 0-无子工序
		sonProcess：工序下的子工序列表{
			sonProcessId：子工序id
			sonProcessOrders：子工序排序
			sonOperate：子工序下的操作列表{
				sonOperateId：子工序的操作id
				sonOperateOrders：子工序的操作排序
			}
		}
		operate：工序下的操作列表{
			operateId：工序的操作id
			operateOrders：工序的操作排序
		}
	}
 *
 *
    返回值：
	content：操作失败！一个产品至少需有一道工序，每道工序下至少需有一项操作！/操作成功
*/
export function chooseProcess(data) {
    return request({
        url: '/processSettings/chooseProcess.do',
        data
    })
}

/*
*   creator: sy 2024-07-18  新增类别
 */
export function addcatCory(data){
    data = data || {};
    return request({
        url:"/processSettings/createCategory.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-07-18  类别的停用/复用
 */
export function catuseory(data){
    data = data || {};
    return request({
        url:"/processSettings/updateCategoryEnabled.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-07-17  创建子工序选项
 */
export function createProcess2(data){
    data = data || {};
    return request({
        url:"/processSettings/createActivity.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-07-22  获取选项管理的统计数据
 */
export function getalloption(data){
    data = data || {};
    return request({
        url:"/processSettings/getOptionManagements.do",
        method: "post",
        data: data
    })
}

/*
*   creator: sy 2024-07-17  获取类别管理列表或是被停用类别数据
 */
export function getcatorylist(data){
    data = data || {};
    return request({
        url:"/processSettings/getCategoryList.do",
        method: "post",
        data: data
    })
}