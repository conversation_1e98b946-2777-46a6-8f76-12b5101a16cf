package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.material.dao.MtOutApplicationItemDao;
import cn.sphd.miners.modules.material.entity.MtOutApplicationItem;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/5/17 11:07
 */
@Repository
public class MtOutApplicationItemDaoImpl  extends BaseDao<MtOutApplicationItem, Serializable> implements MtOutApplicationItemDao {

}
