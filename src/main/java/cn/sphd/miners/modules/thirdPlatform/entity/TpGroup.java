package cn.sphd.miners.modules.thirdPlatform.entity;

import cn.sphd.miners.common.persistence.BaseEntity;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2022年10月24日 11:37
 **/
@Entity
@Table( name ="t_tp_app" )
public class TpGroup extends BaseEntity {
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    @Column(length=50)
    private String name;//`name` VARCHAR(50) DEFAULT NULL COMMENT '名称',

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
