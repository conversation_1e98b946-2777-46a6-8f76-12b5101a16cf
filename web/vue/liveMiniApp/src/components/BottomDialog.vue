<template>
    <div class="BottomDialog">
      <div class="dislogCon">
        <slot></slot>
      </div>
    </div>
</template>
<script setup lang="ts">
</script>
<style lang="scss" scoped>
.BottomDialog{
  position: fixed;
  background: rgba(100,100,100,0.3);
  width: 100%;
  height: 100%;
  top:0;
  left: 0;
  .dislogCon{
    position: absolute;
    bottom: 20px;
    width: 92%;
    margin-left: 4%;
    border-radius: 4px;
    //background: #fff;
  }
  //@keyframes dialog-fade-in {
  //  0% {
  //    transform: translate3d(0, 100%, 0);
  //    opacity: 0;
  //  }
  //  100% {
  //    transform: translate3d(0, 0, 0);
  //    opacity: 1;
  //  }
  //}
  //@keyframes dialog-fade-out {
  //  0% {
  //    transform: translate3d(0, 0, 0);
  //    opacity: 1;
  //  }
  //  100% {
  //    transform: translate3d(0, -100%, 0);
  //    opacity: 0;
  //  }
  //}
  //


}

</style>
