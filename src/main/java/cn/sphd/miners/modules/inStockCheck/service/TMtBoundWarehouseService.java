package cn.sphd.miners.modules.inStockCheck.service;

import cn.sphd.miners.modules.inStockCheck.entity.TMtBoundWarehouse;

import java.util.List;
public interface TMtBoundWarehouseService{

    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyIn(List<Long> list);

    int insert(TMtBoundWarehouse record);

    int insertOrUpdate(TMtBoundWarehouse record);

    int insertOrUpdateSelective(TMtBoundWarehouse record);

    int insertSelective(TMtBoundWarehouse record);

    TMtBoundWarehouse selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TMtBoundWarehouse record);

    int updateByPrimaryKey(TMtBoundWarehouse record);

    int updateBatch(List<TMtBoundWarehouse> list);

    int updateBatchUseMultiQuery(List<TMtBoundWarehouse> list);

    int updateBatchSelective(List<TMtBoundWarehouse> list);

    int batchInsert(List<TMtBoundWarehouse> list);

    int batchInsertSelectiveUseDefaultForNull(List<TMtBoundWarehouse> list);

}
