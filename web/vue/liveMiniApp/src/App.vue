<template>
  <div id="appPage">
    <router-view />
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import JSONBig from 'json-bigint'
import { onMounted } from "vue";
import { useRouter } from "vue-router";
import { sureLogin, callLogin, organizationList, getRootPathFile  } from '@/api/api.js'
import auth from './sys/auth'

  const router2 = useRouter();
  let acceptData = ''

  onMounted(() => {

    // ==================== onMounted 开始 ========================  //

    // 模拟地址uri
// https://hxz.frp.btransmission.com/?u=%7B%22userInfo%22%3A%7B%22nickName%22%3A%22%E7%8E%8B%E8%82%89%E8%82%89%E6%98%AF%E5%A5%B6%E8%90%8C%E5%93%92%22%2C%22avatarUrl%22%3A%22https%3A%2F%2Fp26.douyinpic.com%2Faweme%2F100x100%2Faweme-avatar%2Ftos-cn-avt-0015_aef90220e9a5f8c9b44d4a8f207dc4ec.jpeg%3Ffrom%3D3782654143%22%2C%22gender%22%3A0%2C%22city%22%3A%22%22%2C%22province%22%3A%22%22%2C%22country%22%3A%22%22%2C%22language%22%3A%22%22%7D%2C%22code%22%3A%22hauJ_Vs0mvuYZ-iupyueONCMOjzoio1gmBkzQ2BbQZU4EY4HS7M4RGDYK2ykuoacguCssWOjguOENBpe8azyFYszauRqtGpHuUgCkbNkGOEFCm9TEpYEQXeUwaE%22%7D
    let localUrl = window.location.href
    let jsonData = {}
    let urlJson = {}
    if(localUrl.indexOf('?u=') > -1){
      let allu = localUrl.split('#/')[0]
      let newUrl=allu.split('?u=')[0]
      let uStr = allu.split('?u=')[1]
      uStr = decodeURIComponent(uStr)
      console.log('uStr=', uStr)
      urlJson = JSONBig.parse(uStr)
      console.log('接到的所有参数 urlJson=', urlJson)
      acceptData = urlJson
      if(urlJson.shareback === '1' || urlJson.shareback === '2'){
        // 大分享回来的
        // 小分享回来的
        jsonData = JSONBig.parse(localStorage.getItem('jsonData'))
        jsonData.shareback = urlJson.shareback
        jsonData.oid = urlJson.oid
      }else{
        jsonData = urlJson
        localStorage.removeItem('jsonData')
        localStorage.removeItem('org')
        localStorage.removeItem('user')
      }

    }

    // 正常使用的
    let localJsonData = jsonData
    login(localJsonData)

  // 开发是无需登陆 使用的
    // let user = auth.getUser()
    // console.log('user',user)
    // user.roleCode='super'
    // user.avatarUrl = auth.webRoot + '/upload/' + auth.getAcc().avatar
    // console.log('user.avatarUrl', user, auth.getAcc())
    // jsonData = {
    //   userInfoSys: user,
    //   userInfo: user,
    //   officeList: [auth.getOrg()],
    //   officeItem: auth.getOrg()
    // }
    // localStorage.setItem('jsonData', JSONBig.stringify(jsonData))
    // router2.push('/homeMy')


    // ==================== onMounted 结束 ========================  //


  });

const goOffice = (oid, type) => {
  let userInfo = auth.getUser()
  let jsonDataStr = localStorage.getItem('jsonData')
  let localJsonData = ''
  if(jsonDataStr && jsonDataStr.length > 5){
    localJsonData = JSONBig.parse(jsonDataStr)
  }else{
    localJsonData = {}
  }
  sureLogin({"oid": oid }).then((res)=>{
    localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))
    console.log('进机构存的数据', localJsonData)
    console.log('type=', type)

    if(type === 1){
      router2.push({ name: 'homeManage' });
    }else if(type === 2){
      router2.push({ name: 'memberList' });
    }

  }).catch(err => {
    console.log('baocuole', err)
  })


}
const chargeSuper = (type, oid) => {
  let havOffice = false
  organizationList().then(res => {
    console.log('机构列表返回值', res)
    if(type === 'havJoin'){ // 查询有没有该机构
      res.organizationList.forEach( off => {
        console.log('off.id , oid')
        console.log(off.id , oid)
        if(off.id == oid){
          havOffice = true
        }
      })
      if(havOffice){
        ElMessage.success('您已加入该机构！');
        router2.push({ name: 'officeList' });
      }else{
        router2.push({ name: 'register4' });
      }
    }else{ // 默认查询是不是超管
      res.organizationList.forEach( off => {
        if(off.super){
          havOffice = true
        }
      })
      // 大分享进来的 , 需要判断又没有注册过机构，如果注册过 直接跳列表，没有注册过就跳转注册页面
      // havOffice = true
      if(havOffice){
        router2.push({ name: 'officeList' });
      }else{
        router2.push({ name: 'register1' });
      }
    }


  }).catch( err => {
    ElMessage.error('获取用户信息失败，请稍后重试！');
  })
}
const login = (localJsonData) =>{
  console.log('登陆 login 传进来的 ', localJsonData)
  let data = {
    appId:localJsonData.appId,
    tgCode:'byteDance',
    code: localJsonData.code,
    userInfo: JSONBig.stringify(localJsonData.userInfo)
  }
  console.log('callLogin data', data)
  let routerTopage = 'register1'
  // return false
  callLogin(data).then(res => {
    console.log(' 登陆返回值=', res)
    localJsonData.loginRes = res
    // 登陆接口的处理
    if(res.success>0) { // 已经有私人领域的账号了

      localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))

      if(localJsonData.shareback === '1'){ // 大分享回来的
        console.log('大分享回来的')
        goOffice(localJsonData.oid, 1)

      }else if(localJsonData.shareback === '2'){ // 小分享回来的
        console.log('小分享回来的')
        goOffice(localJsonData.oid, 2)

      }else if(localJsonData.share && localJsonData.share.type ==='1'){ // 大分享进来的
        console.log('大分享进来的 , 需要判断又没有注册过机构，如果注册过 直接跳列表，没有注册过就跳转注册页面')
        // localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))
        chargeSuper()

      }
      else if(localJsonData.share && localJsonData.share.type ==='2'){ // 小分享进来的
        console.log('小分享进来的')
        // localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))
        chargeSuper('havJoin', localJsonData.share.oid)

      }else{ //
        console.log('啥情况都没有进来的')
        // localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))
        router2.push({ name: 'officeList' });

      }

    } else { // 还没有 私人领域的 账号

      let error = res.error
      switch (error.code) {
        case '2': //需要短信验证激活
          ElMessage.error('需要短信验证激活！');
          break
        case '3': //需要注册，调用auth/sendMessageVerificationCodeRegister.do注册通用框架账号

          if(localJsonData.share && localJsonData.share.type ==='1'){ // 大分享进来的
            console.log('大分享进来的')
            localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))
            router2.push({ name: 'register1' });

          }else if(localJsonData.share && localJsonData.share.type ==='2'){ // 小分享进来的
            console.log('小分享进来的')
            localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))
            router2.push({ name: 'register4' });

          }else{ //
            console.log('啥情况都没有进来的')
            localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))
            router2.push({ name: 'register1' });
          }
          break
        case '4': //可能是授权或者code问题，建议重试tt.login
          ElMessage.error('可能是授权或者code问题，建议重试tt.login！');
          break
        case '5': //服务器问题，比如wonderss服务器访问微信/字节服务器异常
          ElMessage.error('服务器问题，比如wonderss服务器访问微信/字节服务器异常');
          console.log('服务器问题，比如wonderss服务器访问微信/字节服务器异常')
          break
      }

    }
  }).catch((err)=>{
    console.log('callLogin err', err)
  })

}

</script>