.clr{ clear: both;  }
.hd{ display: none;    }
li{ list-style: none;  }
.big-btn{ border:1px solid #ddd; display:inline-block; padding:5px 15px; cursor:pointer;     }
.big-btn:hover{ background:#eee; }
.blue{ background:blue; color:#fff;  }
.blue:hover{ background:green;   }
.Accounting{ background:#fff; width:655px; font-size:13px; border-radius:4px; border:1px solid #ccc;    }
.acHead,.acFoot{ font-size:16px;  }
.acHead, .acCon, .acFoot{ padding:10px;   }
.acFoot{ text-align: right; border-top: 1px solid #ccc;  }
.acFoot>p{
    font-size:12px;
    font-weight: 700;
}
.acClose{ background:#fff no-repeat center center; background-size:20px 20px;       }
.acCon{ height:350px; overflow-y:auto;   }
.acCont{ width: 100%; overflow: hidden; }
.acCont>table{ width:100%;    }
.acCont>table tr>td{border:1px solid #ccc; text-align:center; line-height:35px;cursor:default;font-size:14px; }
.acCont>table tr>td:hover{ background:#eee;      }
.acCont>table tr>td.acActive{ background:#ddd;   }


#actsTree .dropDown>ul{ padding:0 0 0 30px;     }
#actsTree li{ line-height:25px;        }
.acInfo{ line-height:30px; padding:8px 30px; cursor:default; position:relative;  }
.acInfo:hover{ background:#eee;        }
.acNo{ padding:0 5px; }
.acInfo.acActive{ background:#ddd;  }

.ac-ye-Icon , .ac-folder-Icon, .drop-acAdd, .drop-acMinus
{ display:inline-block; width:16px; height:16px; /*border:1px solid #ccc;*/   }
.ac-ye-Icon , .ac-folder-Icon { position:relative; top:2px; }
.drop-acAdd , .drop-acMinus{ position:absolute; left:8px;  top:5px;     }
.ac-ye-Icon{ background:url("icon/doc.png") no-repeat center;           }
.ac-folder-Icon{ background:url("icon/folder.png") no-repeat center;    }
.drop-acAdd { background:url("icon/plus.png") no-repeat center;         }
.drop-acMinus{ background:url("icon/minus.png") no-repeat center;       }
.ac-ye-Icon , .ac-folder-Icon, .drop-acAdd, .drop-acMinus{ background-size:15px 15px;    }

.dropDown ul.ulObj_hide{ display: none;  }
.dropDown ul.ulObj_show{ display: block;  }





