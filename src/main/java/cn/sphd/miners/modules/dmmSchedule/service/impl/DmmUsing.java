package cn.sphd.miners.modules.dmmSchedule.service.impl;

import cn.sphd.miners.modules.dmmSchedule.entity.DmmScheduleImage;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmScheduleImageHistory;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmScheduleSupplementImage;
import cn.sphd.miners.modules.dmmSchedule.service.DmmScheduleHistoryService;
import cn.sphd.miners.modules.dmmSchedule.service.DmmScheduleImageService;
import cn.sphd.miners.modules.dmmSchedule.service.DmmScheduleSupplementService;
import cn.sphd.miners.modules.uploads.service.FileUsingCallback;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;

public class DmmUsing implements FileUsingCallback {
    private static final long serialVersionUID = 1L;
    Integer id;
    String entityClass;
    @Override
    public boolean checkUsing(String filename) {
        if(StringUtils.isNotEmpty(filename)) {
            ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
            ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
            switch (entityClass) {
                case "DmmScheduleImage":
                    DmmScheduleImageService dmmScheduleImageService= ac.getBean(DmmScheduleImageService.class, "DmmScheduleImageService");
                    DmmScheduleImage entity = dmmScheduleImageService.getDmmScheduleImageById(id);
                    if (entity != null) {
                        return filename.equals(entity.getNormal());
                    }
                    break;
                case "DmmScheduleImageHistory":
                    DmmScheduleHistoryService dmmScheduleHistoryService = ac.getBean(DmmScheduleHistoryService.class, "DmmScheduleHistoryService");
                    DmmScheduleImageHistory dmmScheduleImageHistory=dmmScheduleHistoryService.getDmmScheduleImageHistoryById(id);
                    if(dmmScheduleImageHistory != null) {
                        return  filename.equals(dmmScheduleImageHistory.getNormal());
                    }
                    break;
                case "DmmScheduleSupplementImage":
                    DmmScheduleSupplementService dmmScheduleSupplementService=ac.getBean(DmmScheduleSupplementService.class,"DmmScheduleSupplementService");
                    DmmScheduleSupplementImage dmmScheduleSupplementImage = dmmScheduleSupplementService.getDmmScheduleSupplementImageById(id);
                    if(dmmScheduleSupplementImage != null) {
                        return  filename.equals(dmmScheduleSupplementImage.getNormal());
                    }
                    break;
            }
        }
        return false;
    }

    @Override
    @JsonIgnore @JSONField(serialize = false)
    public String getKey() {
        /* 由于本回调类是多实体使用，需要使用id+实体类名称作为唯一key；
         * 被UploadFileUsing.callbackKey保存数据库用于删除文件引用。 */
        return id+entityClass;
    }

    public DmmUsing(Integer id, Class entityClass) {
        this.id = id;
        String className = entityClass.getName();
        //实体类判断，去掉包路径，方便后续包改名或者改路径无需修改代码。
        this.entityClass = className.substring(className.lastIndexOf('.')+1);
    }

    public DmmUsing(){

    }

    public Integer getId() {
        return id;
    }

    public String getEntityClass() {
        return entityClass;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public void setEntityClass(String entityClass) {
        this.entityClass = entityClass;
    }
}
