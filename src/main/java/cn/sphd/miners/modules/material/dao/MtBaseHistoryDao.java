package cn.sphd.miners.modules.material.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.entity.MtBase;
import cn.sphd.miners.modules.material.entity.MtBaseHistory;

import java.io.Serializable;

public interface MtBaseHistoryDao extends IBaseDao<MtBaseHistory, Serializable> {

    int insert(MtBase mtBase);
    //添加进历史，不添加关联关系，专门用于 暂停供应商
    int insert2(MtBase mtBase);

    MtBaseHistory getNew(Integer material);
}
