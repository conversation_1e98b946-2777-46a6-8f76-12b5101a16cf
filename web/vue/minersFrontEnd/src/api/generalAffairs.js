import {request} from "@/utils/request";

/*
// creator: hxz ，2024-08-05  员工查询
params:
    isDuty: 1
    edus:
    gender:
    departments:
    posts:
    marry:
    birthday1:
    birthday2:
    onDutyDate1:
    onDutyDate2:
    pageSize: 20
    currentPageNo: 1
*/

export function getScreenList(data) {
    return request({
        url: '/general/employeeList.do',
        data
    })
}
/*
* creator: hxz ，2024-08-06  获取部门列表
*  orgId: 4239
*  orgType: 2
*  userId: 8931
*
* */

export function getDepartMentList(data) {
    return request({
        url: '/post/toPostManagement.do',
        data
    })
}


/*
* creator: hxz ，2024-08-06  获取岗位列表
*
* */

export function getPostList() {
    return request({
        url: '/post/getPostList.do',
    })
}


/*
* creator: hxz ，2024-08-06  新增员工 的 基础数据
*
* */

export function getSelect() {
    return request({
        url: '/recruit/getSelect.do',
    })
}

/*
* creator: hxz ，2024-08-06  获取子级部门
*  id: 父级部门的 id
* */

export function checkSonDepartment(data) {
    return request({
        url: '/general/checkSonDepartment.do',
        data
    })
}
/*
* creator: hxz ，2024-08-13  点击批量导入-按钮 ，判断是否有未完成的导入
* */

export function unfinishedImportUser() {
    return request({
        url: '/userImport/unfinishedImportUser.do',
    })
}


/*
* creator: hxz ，2024-08-06  新增员工
*  params:
        userName: hxz
        mobile: 15202255155
        date1: 2024/08/09
        department: 4285
        postID: 51
        postName: 销售
        ordinaryEmployees: 0
        managerCode: general
        pid: 8930
* */

export function addUser(data) {
    return request({
        url: '/general/addUser.do',
        data
    })
}

/*
* creator: hxz ，2024-08-13  点击 导入
* */
export function newImportUser(data) {
    return request({
        url: '/export/newImportUser.do',
        data
    })
}

/*
* creator: hxz ，2024-08-13 下一步-数据保存
* */
export function saveImportUser(data) {
    return request({
        url: '/userImport/saveImportUser.do',
        data
    })
}


/*
* creator: hxz ，2024-08-13 放弃
* */
export function giveUpImportUser() {
    return request({
        url: '/userImport/giveUpImportUser.do',
    })
}

/*
* creator: hxz ，2024-08-13 最后保存确定
* */
export function completeImportUser() {
    return request({
        url: '/userImport/completeImportUser.do',
    })
}


/*
* creator: hxz ，2024-08-13 择直接上级 列表
* */
export function selectOptionalLeader(data) {
    return request({
        url: '/userImport/selectOptionalLeader.do',
        data
    })
}



/*
* creator: hxz ，2024-08-13 输入手机号 查重接口
* param:
*   "mobile":
    "userName":
    "sonOid" // manage.jsp中枢管控，分支机构用的sonOid，其他页面不需要
* */
export function updateFalseUserEnter(data) {
    return request({
        url: '/userImport/updateFalseUserEnter.do',
        data
    })
}


/*
* creator: hxz ，2024-08-15 导入 修改 直接上级
* param:
*   leaderSorce: 1
    leaderId: 1513
    userId: 1512
* */
export function updateImportUser(data) {
    return request({
        url: '/userImport/updateImportUser.do',
        data
    })
}



/*
* creator: hxz ，2024-08-15 导入 修改 直接上级
* param:
*   "usersList": list,
    "importSum": sum
    sonOid //  manage.jsp中枢管控，分支机构用的sonOid，其他页面不需要
* */
export function allImportUserEnter(data) {
    return request({
        url: '/userImport/allImportUserEnter.do',
        data
    })
}




/*
* creator: hxz ，2024-08-16 基本信息编辑提交
* param:
*   "userInfo": json,
    "contacts": []
* */
export function updateUserBasicInfo(data) {
    return request({
        url: '/userInfo/updateUserBasicInfo.do',
        data
    })
}


/*
* creator: hxz ，2024-08-16 基本信息修改记录
* param:
*    passiveUserId: employeeId,
    pageSize: pageSize,
    currentPageNo: currPage
* */
export function getUserBasicInfoHistory(data) {
    return request({
        url: '/userInfo/getUserBasicInfoHistory.do',
        data
    })
}

/*
* creator: hxz ，2024-08-16 修改记录查看
* param:
*   'id': id
* */
export function getUserHistory(data) {
    return request({
        url: '/userInfo/getUserHistory.do',
        data
    })
}


/*
* creator: hxz ，2024-08-16 新增教育经历
* param:
*   passiveUserId: userID
*   beginTime , endTime , collegeName , departmentName, major, degreeDesc,
*   majorDesc, memo
*
* */
export function addPersonalEducation(data) {
    return request({
        url: '/userInfo/addPersonalEducation.do',
        data
    })
}


/*
* creator: hxz ，2024-08-16 新增教育经历
* param:
*   passiveUserId: userID
*   beginTime , endTime , collegeName , departmentName, major, degreeDesc,
*   majorDesc, memo
*
* */
export function updatePersonalEducation(data) {
    return request({
        url: '/userInfo/updatePersonalEducation.do',
        data
    })
}


/*
* creator: hxz ，2024-08-16 教育经历修改记录
* param:
*   id: id,
    pageSize: pageSize,
    currentPageNo: currPage
*
* */
export function personalEducationHistories(data) {
    return request({
        url: '/userInfo/personalEducationHistories.do',
        data
    })
}


/*
* creator: hxz ，2024-08-16 教育经历修改记录 详情
* param:
*   id: id,
*
* */
export function getPersonalEducationHistory(data) {
    return request({
        url: '/userInfo/getPersonalEducationHistory.do',
        data
    })
}


/*
* creator: hxz ，2024-08-16 删除本条教育经历
* param:
*   id: id,
*
* */
export function markDeleteEducation(data) {
    return request({
        url: '/userInfo/markDeleteEducation.do',
        data
    })
}


/*
* creator: hxz ，2024-08-16 新增工作经历
* param:
*   passiveUserId, beginTime, endTime, corpName, corpSize, corpNature, corpDepartment,
*  post, majorDesc, memo
*
* */
export function addPersonnelOccupation(data) {
    return request({
        url: '/userInfo/addPersonnelOccupation.do',
        data
    })
}


/*
* creator: hxz ，2024-08-16 新增工作经历
* param:
*   passiveUserId, id, beginTime, endTime, corpName, corpSize, corpNature, corpDepartment,
*  post, majorDesc, memo
*
* */
export function updatePersonnelOccupation(data) {
    return request({
        url: '/userInfo/updatePersonnelOccupation.do',
        data
    })
}


/*
* creator: hxz ，2024-08-19 工作经历修改记录
* param:
*   id: id,
    pageSize: pageSize,
    currentPageNo: currPage
*
* */
export function personnelOccupationHistories(data) {
    return request({
        url: '/userInfo/personnelOccupationHistories.do',
        data
    })
}

/*
* creator: hxz ，2024-08-19 工作经历 修改记录查看
* param:
*   id: id,
*
* */
export function getPersonnelOccupationHistory(data) {
    return request({
        url: '/userInfo/getPersonnelOccupationHistory.do',
        data
    })
}

/*
* creator: hxz ，2024-08-19 工作经历删除确定
* param:
*   id: id,
*
* */
export function markDeleteOccupation(data) {
    return request({
        url: '/userInfo/markDeleteOccupation.do',
        data
    })
}


/*
* creator: hxz ，2024-08-19 工作经历删除确定
* param:
*   passiveUserId , idCard, address
*
* */
export function updateUserIdCard(data) {
    return request({
        url: '/userInfo/updateUserIdCard.do',
        data
    })
}


/*
* creator: hxz ，2024-08-19 工作经历删除确定
* param:
*    passiveUserId: employeeId
*
* */
export function getUserIdCardHistories(data) {
    return request({
        url: '/userInfo/getUserIdCardHistories.do',
        data
    })
}

/*
* creator: hxz ，2024-08-19 身份证 修改记录查看
* param:
*      id
*
* */
export function getUserIdCard(data) {
    return request({
        url: '/userInfo/getUserIdCard.do',
        data
    })
}

/*
* creator: hxz ，2024-08-19  员工详情
* param:
*      passiveUserId
*
* */
export function getUserArchives(data) {
    return request({
        url: '/userInfo/getUserArchives.do',
        data
    })
}

/*
* creator: hxz ，2024-08-19  档案管理提交
* param:
*      userID,
*
* */
export function updateEssentialInformation(data) {
    return request({
        url: '/general/updateEssentialInformation.do',
        data
    })
}


/*
* creator: hxz ，2024-08-19  办理离职
* param:
*      userId,memo
*
* */
export function forLeaving(data) {
    return request({
        url: '/general/forLeaving.do',
        data
    })
}

/*
* creator: hxz ，2024-08-19  部门列表
* param:
*      orgType=2
*
* */
export function getAllManageAndPosts(data) {
    return request({
        url: '/general/getAllManageAndPosts.do',
        data
    })
}


/*
* creator: hxz ，2024-08-19  可选择的机构列表
* param:
*
* */
export function getOrgSonOrgs() {
    return request({
        url: '/sonOrg/getOrgSonOrgs.do',
    })
}


/*
* creator: hxz ，2024-08-19  删除职工 已导入的
* param:
*       id， sonOid
*
* */
export function deleteImportUser(data) {
    return request({
        url: '/userImport/deleteImportUser.do',
        data
    })
}


/*
* creator: hxz ，2024-08-19  修改职工信息确定
* param:
*        "id", "mobile", "userName"
*
* */
export function updateImportUserMobile(data) {
    return request({
        url: '/userImport/updateImportUserMobile.do',
        data
    })
}


/*
* creator: hxz ，2024-08-26   总务 职工档案 异常账户-即将冻结的账号列表接口
* param:
*        pageSize , currentPageNo
*
* */
export function soonFrozenUsers(data) {
    return request({
        url: '/general/soonFrozenUsers.do',
        data
    })
}


/*
* creator: hxz ，2024-08-26   总务 职工档案 异常账户-已冻结的账号列表接口
* param:
*        pageSize 每页条数， currentPageNo 当前页数
*
* */
export function frozenUsers(data) {
    return request({
        url: '/general/frozenUsers.do',
        data
    })
}


/*
* creator: hxz ，2024-08-26   总务 职工档案 帮他激活 发送短信验证码
* param:
*        phone 手机号
*
* 返回值： success 1 发送成功 / success 0 验证失败 抛出失败信息 “手机号长度不对”，“手机号格式不对”， “验证码未过时”。
* */
export function helpActivationVerificationCode(data) {
    return request({
        url: '/general/helpActivationVerificationCode.do',
        data
    })
}

/*
* creator: hxz ，2024-08-26  总务 职工档案 帮他激活 验证 短信验证码
* param:
*         phone 手机号， code 验证码
* 返回值： success 1 验证通过 / success 0 验证失败 抛出失败信息 ”验证码错误或者超时！“
*
* response:{"page":null,"success":1,"data":"发送成功","error":null,"others":null}
* */
export function checkHelpActivationVerification(data) {
    return request({
        url: '/general/checkHelpActivationVerification.do',
        data
    })
}


/*
* creator: hxz ，2024-08-26  总务 职工档案 帮他激活
* param:
*          passiveUserId 要激活的 id
*
* response:{"page":null,"success":1,"data":"发送成功","error":null,"others":null}
* */
export function activationUser(data) {
    return request({
        url: '/general/activationUser.do',
        data
    })
}


/*
* creator: hxz ，2024-10-31  输入手机号 查重接口
* param:
*   mobile
*   passiveUserId
*
*  */
export function mobileCheckRepeat(data) {
    return request({
        url: '/userInfo/mobileCheckRepeat.do',
        data
    })
}

/*
* creator: hxz ，2024-10-31  更新手机号确定
* param:
*    "userId": uId,
     "phone": mobile
*
*  */
export function updateUserMobile(data) {
    return request({
        url: '/userInfo/updateUserMobile.do',
        data
    })
}
/*
* creator: 李玉婷 ，2025-01-15  新增合同
 */
export function addEmpContract(data) {
    return request({
        url: '/perCon/insertPersonnelContract.do',
        data
    })
}
/*
* creator: 李玉婷 ，2025-01-15  新增合同
 */
export function changeEmpContract(data) {
    return request({
        url: '/perCon/updatePersonnelContract.do',
        data
    })
}
/*
* creator: 李玉婷 ，2025-01-15  新增合同
 */
export function renewEmpContract(data) {
    return request({
        url: '/perCon/renewalPersonnelContract.do',
        data
    })
}
/*
* creator: 李玉婷 ，2025-01-15  查看未到期合同详细
 */
export function getPersonnelContractMessage(id) {
    return request({
        url: '/perCon/getPersonnelContractMessage.do',
        data: {
            id: id
        }
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 获取合同的修改记录
export function getEmpContractChangeHis(id) {
    return request({
        url: '/perCon/personnelContractHistory.do',
        data: {
            id: id //合同id
        }
    })
}
// creator: 李玉婷，2024-08-06 02:27:24， 获取某个合同的全部续约记录
export function getEmpContractRenewHis(id) {
    return request({
        url: '/perCon/getPersonnelContractSignRecord.do',
        data: {
            userID: id
        }
    })
}

// creator: 李玉婷，2024-08-06 02:27:24， 获取某个合同修改记录的详情
export function getEmpContractChangeHisDetail(id) {
    return request({
        url: '/perCon/getPersonnelContractHisMes.do',
        data: {
            contractHisId: id
        }
    })
}
// creator: 张旭博，2024-08-06 02:27:24， 获取某个合同修改记录的详情
export function stopPersonnelContract(id) {
    return request({
        url: '/perCon/terminatePersonnelContract.do',
        data: {
            userID: id
        }
    })
}

//================== 跨机构管理=========

// creator: 李玉婷，2025-07-10 08:02:11， 获取操作日志
export function getMonthHistories(data) {
    return request({
        url: '../sonOrg/getSeeOrgMonthHistories.do',
        data: data
    })
}

// creator: 李玉婷，2025-07-11 09:55:36， 跨机构管理主页面数据
export function spanOrgManage(data) {
    return request({
        url: '../sonOrg/spanOrgManage.do',
        data: data
    })
}

// creator: 李玉婷，2025-07-11 14:05:45， 跨机构管理主页面数据
export function getUserSeeOrgs(data) {
    return request({
        url: '../sonOrg/getUserSeeOrgs.do',
        data: data
    })
}
// creator: 李玉婷，2025-07-11 15:39:15， 跨机构管理主页面数据
export function updateUserSeeOrgs(data) {
    return request({
        url: '../sonOrg/updateUserSeeOrgs.do',
        data: data
    })
}

// creator: 李玉婷，2025-07-14 08:05:26， 点击查看日历某日的操作历史记录接口
export function getSeeOrgDayHistories(data) {
    return request({
        url: '../sonOrg/getSeeOrgDayHistories.do',
        data: data
    })
}



