package cn.sphd.miners.modules.material.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.entity.MtSupplierMaterialHistory;
import cn.sphd.miners.modules.material.entity.MtSupplierMaterial;

import java.io.Serializable;

/**
 * Created by Administrator on 2016/9/19.
 */
public interface MtSupplierMaterialHistoryDao extends IBaseDao<MtSupplierMaterialHistory,Serializable>{

    MtSupplierMaterialHistory saveHis(MtSupplierMaterial mtSupplierMaterial,MtSupplierMaterialHistory mtSupplierHistory);
}
