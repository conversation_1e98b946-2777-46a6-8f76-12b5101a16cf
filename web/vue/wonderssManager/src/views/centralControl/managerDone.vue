<template>
  <Pageheader :title="title" :showBack="true"></Pageheader>
  <div class="container_main">
    <el-row style="margin-top: 48px;">
      <el-col :span="20" :offset="2">
        <span v-show="params.code === 'accounting'">当前贵公司会计业务{{params.userInfo.agentType === 1?'由':'不是'}}客必盛代理</span><br>
        <span v-if="params.code === 'general'"><b>{{params.userInfo.userName}}</b> 为总务权限的分配者。</span>
        <span v-if="params.code === 'finance'"><b>{{params.userInfo.userName}}</b> 为财务权限的分配者。</span>
        <span v-if="params.code === 'sale'"><b>{{params.userInfo.userName}}</b> 为销售权限的分配者。</span>
        <span v-if="params.code === 'accounting'"><b>{{params.userInfo.userName}}</b> 为会计事务的负责人。</span>
        <span v-if="params.code === 'smallSuper'">您已选择 <b>{{params.userInfo.userName}}</b> 为全权负责人。</span>
        <br>

      </el-col>
    </el-row>
    <el-row style="margin-bottom: 48px">
      <el-col :span="20" :offset="2">
        <span>手机号：{{params.userInfo.mobile}}。</span>
      </el-col>
    </el-row>
    <el-row v-if="params.initState === '1'" style="margin-bottom: 48px">
      <el-col :span="20" :offset="2">
        <div class="ty-right">
          <el-button type="primary" size="large" link @click="chargeHistory">历任负责人</el-button>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="20" :offset="2" style="text-align: center">
        <el-button type="primary" size="large" @click="changeManager">换为他人</el-button>
        <el-button type="primary" size="large" v-show="params.code === 'smallSuper'" @click="noSet">暂不再设置</el-button>
        <el-button type="primary" size="large" v-show="params.code === 'accounting'" @click="changeAgent">修改代理状态</el-button>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import * as api from "@/api/centralControl";
import auth from '@/sys/auth'

export default {
  data() {
    return {
      title: '',
      params: {
        initState: '',
        code: '',
        userInfo: {
          userID: 0,
          userName: '',
          mobile: '',
          agentType: 0,
          isHasCharge: false,
          manageName: ''
        }
      },
      agentForm: {
        userName: '',
        mobile: ''
      },
      passiveUser: '',
      option_users: []
    }
  },
  created() {
    this.title = auth.getUser().roleCode === 'super'?'核心人物管控':'高管管理'
    this.params = {
      initState: this.$store.getters.getManage, // 是否已经全部完成
      code: this.$store.getters.getManageCode, // 要编辑的高管code
      userInfo: this.$store.getters.getManageParam // 用户信息
    }
    console.log('params', this.params)
    let code = this.$route.params.code
    let type = this.$route.params.type

    let promise = api.getSelectStaffUsers()
    promise.then(res => {
      let data = res.data.data
      console.log('data:::' , data)
      let filterData = data.filter(item => !(item.roleCode === 'super' && code === 'smallSuper'))
      let roleArr = filterData.map(item => {
        return {
          value: item.userID,
          label: (item.userName + ' ' + item.mobile)
        }
      })
      this.option_users = roleArr
    })
  },
  methods: {
    changeManager() {
      // 当前人是超管且设置了全权负责人并且是其他高管，不让设置
      if (this.params.userInfo.isHasCharge && auth.getUser().roleCode === 'super' && this.params.code !== 'smallSuper') {
        this.$message({
          type: 'error',
          message: '操作失败！<br>因为您已选择了全权负责人！',
          dangerouslyUseHTMLString: true
        })
        return false
      }
      this.$router.push({
        path: '/managerInput/change'
      })
    },
    noSet() {
      this.$messageBox.confirm(
          '确定后，各项事务的负责人将由您直接管控。<br>确定暂不再设置全权负责人吗？',
          '！！提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
      )
          .then(() => {
            api.noSmallSuper(this.params.userInfo.userID)
            .then(res => {
              let data = res.data.data
              let status = data.status
              if (status === 1) {
                this.$message({
                  type: 'success',
                  message: '操作成功！',
                })
                this.$router.go(-1)
              } else {
                this.$message({
                  type: 'error',
                  message: '操作失败！',
                })
              }
            })
          })
    },
    changeAgent() {
      if (this.params.userInfo.isHasCharge && auth.getUser().roleCode === 'super') {
        this.$message({
          type: 'error',
          message: '操作失败！<br>因为您已选择了全权负责人！',
          dangerouslyUseHTMLString: true
        })
        return false
      }
      let agentType = this.params.userInfo.agentType
      let tip = ''
      if (agentType === 1) {
        tip = '当前贵公司会计业务由客必盛代理。<br>不再由客必盛代理？'
      } else {
        tip = '当前贵公司会计业务不是客必盛代理。<br>修改为由客必盛代理？'
      }
      this.$messageBox.confirm(
          tip,
          '！！提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
      )
          .then(() => {
            this.$router.push({
              path: '/managerInput/changeAgent'
            })
          })
    },
    chargeHistory() {
      this.$router.push({
        path: '/chargeHistory'
      })
    },
    testMobile(val) {
      let pattern =/^(0|86|17951)?(13[0-9]|15[0-9]|16[0-9]|17[0-9]|18[0-9]|14[0-9]|19[0-9])[0-9]{8}$/
      console.log('pattern.exec(val)', pattern.exec(val))
      return pattern.exec(val)
    }
  }
}

</script>

<style lang="scss" scoped>
.el-row {
  margin-bottom: 16px;
}
.el-row:last-child {
  margin-bottom: 0;
}
</style>
