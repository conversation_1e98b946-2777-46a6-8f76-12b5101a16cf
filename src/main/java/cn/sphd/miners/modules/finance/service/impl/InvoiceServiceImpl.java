package cn.sphd.miners.modules.finance.service.impl;

import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.finance.dao.*;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.InvoiceService;
import cn.sphd.miners.modules.sales.dao.SlInvoiceApplicationDetailDao;
import cn.sphd.miners.modules.sales.entity.SlInvoiceApplicationDetail;
import cn.sphd.miners.modules.system.dao.LocalSettingDao;
import cn.sphd.miners.modules.system.entity.LocalSetting;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * Created by Administrator on 2017/7/10.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class InvoiceServiceImpl extends BaseServiceImpl implements InvoiceService {

    @Autowired
    FinanceInvoiceSettingDao financeInvoiceSettingDao;
    @Autowired
    FinanceInvoiceDao financeInvoiceDao;
    @Autowired
    FinanceInvoiceDetailDao financeInvoiceDetailDao;
    @Autowired
    FinanceInvoiceSettingHistoryDao financeInvoiceSettingHistoryDao;
    @Autowired
    FinanceInvoiceRateHistoryDao financeInvoiceRateHistoryDao;
    @Autowired
    FinanceInvoiceDetailHistoryDao financeInvoiceDetailHistoryDao;
    @Autowired
    LocalSettingDao localSettingDao;
    @Autowired
    SlInvoiceApplicationDetailDao invoiceApplicationDetailDao;

    @Override
    public FinanceInvoiceSetting getFinanceInvoiceSetting(Integer oid,String category,Integer invoiceSettineId) {
        HashMap<String, Object> params = new HashMap<>();
        List<String> where = new ArrayList<>();
        StringBuffer hql = new StringBuffer(" from FinanceInvoiceSetting");
        if (oid!=null){
            where.add(" org_=:oid");
            params.put("oid",oid);
        }
        if (!"".equals(category) && category!=null){
            where.add(" category=:category");
            params.put("category",category);
        }
        if (invoiceSettineId!=null){
            where.add(" id=:invoiceSettineId");
            params.put("invoiceSettineId",invoiceSettineId);
        }
        if (!where.isEmpty()){
            hql.append(" where ").append(org.apache.commons.lang3.StringUtils.join(where," and "));
        }
        FinanceInvoiceSetting financeInvoiceSetting = (FinanceInvoiceSetting) financeInvoiceSettingDao.getByHQLWithNamedParams(hql.toString(),params);
        return financeInvoiceSetting;
    }

    @Override
    public List<FinanceInvoiceSetting> getFinanceInvoiceSettingByOid(Integer oid) {
        String hql = " and o.organization = "+oid;
        return financeInvoiceSettingDao.findCollectionByConditionNoPage(hql,null,null);
    }

    @Override
    public void updateFinanceInvoiceSetting(FinanceInvoiceSetting financeInvoiceSetting) {
        financeInvoiceSettingDao.update(financeInvoiceSetting);
    }

    @Override
    public void addFinanceInvoiceSetting(FinanceInvoiceSetting financeInvoiceSetting) {
        financeInvoiceSettingDao.save(financeInvoiceSetting);
    }

    @Override
    public void addFinanceInvoice(FinanceInvoice financeInvoice) {
        financeInvoiceDao.save(financeInvoice);
    }

    @Override
    public void addFinanceInvoiceDetail(FinanceInvoiceDetail financeInvoiceDetail) {
        financeInvoiceDetailDao.save(financeInvoiceDetail);
    }

    @Override    //orderBy 1-原来PC端的是倒序    type1-增值税专用票,2-增值税普通票,3-其它普通票
    public List<Map<String,Object>> getAllInvoices(Integer oid,Date beginDate,Date endDate,String type,Integer orderBy) {
        Map<String,Object> map = new HashMap<>();
//        String hql = "from FinanceInvoice where org_=:org";
//        String hql = "select f.id,f.beginNo,f.endNo,f.invoiceCode,f.type,f.buyerName,f.buyDate,f.createName,f.createDate,count(fd.id) from FinanceInvoice f,FinanceInvoiceDetail fd where f.org_=:org and fd.state in ('1','4') and f.id=fd.invoiceReg_";
        String hql = "select f.id,f.beginNo,f.endNo,f.invoiceCode,f.type,f.buyerName,f.buyDate,f.createName,f.createDate from FinanceInvoice f where f.org_=:org";
        map.put("org",oid);
        if (beginDate!=null&&endDate!=null){
            hql+=" and f.createDate>=:beginDate and f.createDate<=:endDate";
            map.put("beginDate",beginDate);
            map.put("endDate",endDate);
        }
        if (!MyStrings.nulltoempty(type).isEmpty()){
            hql+=" and f.type=:type";
            map.put("type",type);
        }
//        hql+=" group by f.id";
        if (orderBy!=null&&1==orderBy){
            hql+=" order by f.id desc";
        }

        List<Object[]> financeInvoices = financeInvoiceDao.getListByHQLWithNamedParams(hql,map);
        List<Map<String,Object>> mapList = new ArrayList<>();
        for (Object[] ob:financeInvoices) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("id",ob[0]);
            map1.put("beginNo",ob[1]);
            map1.put("endNo",ob[2]);
            map1.put("invoiceCode",ob[3]);
            map1.put("type",ob[4]);
            map1.put("buyerName",ob[5]);
            map1.put("buyDate",ob[6]);
            map1.put("createName",ob[7]);
            map1.put("createDate",ob[8]);

            Map<String,Object> map2 = new HashMap<>();
            String hql1 = "select count(id) from FinanceInvoiceDetail where invoiceReg_=:invoiceReg and state in ('1','4')";
            map2.put("invoiceReg",ob[0]);
            Long detailNum = (Long) financeInvoiceDao.getByHQLWithNamedParams(hql1,map2);
            map1.put("detailNum",detailNum==null?0:detailNum.intValue());

            mapList.add(map1);
        }
        return mapList;
//        return financeInvoices;
    }

    @Override    //orderBy 1-原来PC端的是倒序    type1-增值税专用票,2-增值税普通票,3-其它普通票
    public List<FinanceInvoice> getAllInvoicesByOid(Integer oid,Date beginDate,Date endDate,String type,Integer orderBy) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from FinanceInvoice where org_=:org";
        map.put("org",oid);
        if (beginDate!=null&&endDate!=null){
            hql+=" and createDate>=:beginDate and createDate<=:endDate";
            map.put("beginDate",beginDate);
            map.put("endDate",endDate);
        }
        if (!MyStrings.nulltoempty(type).isEmpty()){
            hql+=" and type=:type";
            map.put("type",type);
        }
        if (orderBy!=null&&1==orderBy){
            hql+=" order by id desc";
        }
        List<FinanceInvoice> financeInvoices = financeInvoiceDao.getListByHQLWithNamedParams(hql,map);
        return financeInvoices;
    }

    @Override
    public List<FinanceInvoiceDetail> getAllInvoiceDetailsByInvoiceId(Integer invoiceId,Date beginDate,Date endDate,String type,Integer oid) {
        Map<String,Object> map = new HashMap<>();
        String hql = " from FinanceInvoiceDetail where invoiceReg_ in (select id from FinanceInvoice where org_=:org";
        map.put("org",oid);
        if (StringUtils.isNotEmpty(type)){
            hql+=" and type=:type)";
            map.put("type",type);
        }else {
            hql+=")";
        }
        if (invoiceId!=null){
            hql+=" and invoiceReg_=:invoiceReg";
            map.put("invoiceReg",invoiceId);
        }
        if (beginDate!=null&&endDate!=null){
            hql+=" and operateDate between :beginDate and :endDate";
            map.put("beginDate",beginDate);
            map.put("endDate",endDate);
        }
        List<FinanceInvoiceDetail> financeInvoiceDetails = financeInvoiceDetailDao.getListByHQLWithNamedParams(hql,map);
        return financeInvoiceDetails;
    }

    @Override
    public FinanceInvoiceDetail getFinanceInvoiceDetailById(Integer id) {
        FinanceInvoiceDetail financeInvoiceDetail = financeInvoiceDetailDao.get(id);
        return financeInvoiceDetail;
    }

    @Override
    public void updateFinanceInvoiceDetail(FinanceInvoiceDetail financeInvoiceDetail) {
        financeInvoiceDetailDao.update(financeInvoiceDetail);

    }

    @Override
    public List<FinanceInvoiceSettingHistory> getFinanceInvoiceSettingHistoryByInvoiceSettingId(Integer invoiceSettineId) {
        String condition = " and o.previousId!=0 and o.settingId = "+invoiceSettineId;
        List<FinanceInvoiceSettingHistory> invoiceSettingHistories = financeInvoiceSettingHistoryDao.findCollectionByConditionNoPage(condition,null,null);
        return invoiceSettingHistories;
    }

    @Override
    public FinanceInvoiceSettingHistory getFinanceInvoiceSettingHistoryById(Integer invoiceSettingHistoryId,Integer previousId) {
        HashMap<String, Object> params = new HashMap<>();
        List<String> where = new ArrayList<>();
        StringBuffer hql = new StringBuffer(" from FinanceInvoiceSettingHistory");
        if (invoiceSettingHistoryId!=null){
            where.add(" id=:invoiceSettingHistoryId");
            params.put("invoiceSettingHistoryId",invoiceSettingHistoryId);
        }
        if (previousId!=null){
            where.add(" previousId=:previousId");
            params.put("previousId",previousId);
        }
        if (!where.isEmpty()){
            hql.append(" where ").append(org.apache.commons.lang3.StringUtils.join(where," and "));
        }
        FinanceInvoiceSettingHistory financeInvoiceSettingHistory = (FinanceInvoiceSettingHistory) financeInvoiceSettingHistoryDao.getByHQLWithNamedParams(hql.toString(),params);
        return financeInvoiceSettingHistory;
    }

    @Override
    public FinanceInvoiceSettingHistory getPreviousInvoiceSetting(Integer invoiceSettineId) {
        String hql = "and o.settingId="+invoiceSettineId+" order by id desc";
        List<FinanceInvoiceSettingHistory> financeInvoiceSettingHistories = financeInvoiceSettingHistoryDao.findCollectionByConditionNoPage(hql,null,null);
        FinanceInvoiceSettingHistory financeInvoiceSettingHistory = new FinanceInvoiceSettingHistory();
        if (financeInvoiceSettingHistories.size()>0){
            financeInvoiceSettingHistory = financeInvoiceSettingHistories.get(0);
        }
        return financeInvoiceSettingHistory;
    }

    @Override
    public void addFinanceInvoiceRateHistory(FinanceInvoiceRateHistory financeInvoiceRateHistory) {
        financeInvoiceRateHistoryDao.save(financeInvoiceRateHistory);
    }

    @Override
    public void addFinanceInvoiceSettingHistory(FinanceInvoiceSettingHistory financeInvoiceSettingHistory) {
        financeInvoiceSettingHistoryDao.save(financeInvoiceSettingHistory);
    }

    @Override
    public List<FinanceInvoiceRateHistory> getFinanceInvoiceRateHistoryBySettingId(Integer settingId,Integer settingHistoryId,BigDecimal rate,Integer maxTaxRateLogId,Integer type) {
        String hql = " from FinanceInvoiceRateHistory where setting = "+settingId;
        if (settingHistoryId!=null){
            hql+=" and settingHistoryId="+settingHistoryId;
        }
        if (rate!=null){
            hql+=" and rate ="+rate;
        }
        if (maxTaxRateLogId!=null){
            hql+=" and id>0 and id<="+maxTaxRateLogId;
        }
        if (type!=null && type==1){
            hql+= " group by rate";
        }

        return financeInvoiceRateHistoryDao.getListByHQL(hql);
    }

    @Override
    public void updateFinanceInvoiceSettingHistory(FinanceInvoiceSettingHistory financeInvoiceSettingHistory) {
        financeInvoiceSettingHistoryDao.update(financeInvoiceSettingHistory);
    }

    @Override
    public Integer getMaxTaxRateHistoryId(Integer settingId, Integer settingHistoryId) {
        HashMap<String, Object> params = new HashMap<>();
        List<String> where = new ArrayList<>();
        StringBuffer hql = new StringBuffer(" select max(id) from FinanceInvoiceRateHistory");
        if (settingId!=null){
            where.add("setting=:settingId");
            params.put("settingId",settingId);
        }
        if (settingHistoryId!=null){
            where.add("settingHistoryId=:settingHistoryId");
            params.put("settingHistoryId",settingHistoryId);
        }
        if (!where.isEmpty()){
            hql.append(" where ").append(org.apache.commons.lang3.StringUtils.join(where," and "));
        }
        Integer maxTaxRateLogId = (Integer) financeInvoiceRateHistoryDao.getByHQLWithNamedParams(hql.toString(),params);  //查到的最大的税率历史id
        return maxTaxRateLogId;
    }

    @Override           //可用的支票
    public List<FinanceInvoiceDetail> getInvoiveDetailByOrgAndType(Integer oid, String type) {
        HashMap<String, Object> params = new HashMap<>();   //包括已使用但是被终止申请的发票
        String hql = "from FinanceInvoiceDetail where invoiceReg_ in (select id from FinanceInvoice where org_=:oid) and (state=1 or state=4 or (state=2 and teminateLabel=1))";
        if(type!=null){
            if ("4".equals(type)) {
                hql+=" and type in ('2','3')";  //2-增值税普通票,3-其它普通票
            }else {
                hql+=" and type=:type";
                params.put("type",type);
            }
        }
        params.put("oid",oid);
        List<FinanceInvoiceDetail> financeInvoiceDetails = financeInvoiceDetailDao.getListByHQLWithNamedParams(hql,params);
        return financeInvoiceDetails;
    }

    /**
     *<AUTHOR>
     *@date 2019/1/22 10:31
     *订单选择发票(使用的service方法)
     */
    @Override
    public void chooseInvoice(Integer invoiceDetailId,Integer lines, Date operateDate,BigDecimal amount,String customterName,String applicantName, Integer applicant,Date applicationTime,Integer customterId,User user,String source) {
        FinanceInvoiceDetail financeInvoiceDetail = financeInvoiceDetailDao.get(invoiceDetailId);
        String stateOld =financeInvoiceDetail.getState();
        financeInvoiceDetail.setOperateDate(operateDate); //开票日期
        if (financeInvoiceDetail.getOperateDate()!=null){
            financeInvoiceDetail.setPeroid(NewDateUtils.getYearMonth(financeInvoiceDetail.getOperateDate()));
        }
        financeInvoiceDetail.setLines(lines);
        financeInvoiceDetail.setState("2");  //状态:1-空白,2-已使用,3-作废,4-启用
        financeInvoiceDetail.setReceiveCorpId(customterId);   //客户id
        financeInvoiceDetail.setReceiveCorp(customterName); //客户名称
        financeInvoiceDetail.setApplicantName(applicantName);  //申请人
        financeInvoiceDetail.setApplicant(applicant);  //申请人id
        financeInvoiceDetail.setApplicationTime(applicationTime);  //申请时间
        financeInvoiceDetail.setAmount(amount);  //金额
        financeInvoiceDetail.setSource(source);  //来源:1-财务录入,2-销售
        financeInvoiceDetail.setUpdator(user.getUserID());
        financeInvoiceDetail.setUpdateName(user.getUserName());
        financeInvoiceDetail.setUpdateDate(new Date());
        financeInvoiceDetailDao.update(financeInvoiceDetail);

        FinanceInvoiceDetailHistory financeInvoiceDetailHistoryLast = getInvoiceDetailHistoryLast(invoiceDetailId); //获取上次修改

        FinanceInvoiceDetailHistory financeInvoiceDetailHistory = new FinanceInvoiceDetailHistory();
        BeanUtils.copyProperties(financeInvoiceDetail,financeInvoiceDetailHistory);
        financeInvoiceDetailHistory.setInvoiceDetail(financeInvoiceDetail);
        financeInvoiceDetailHistory.setInvoiceDetailId(financeInvoiceDetail.getId());
        financeInvoiceDetailHistory.setCreateName(user.getUserName());
        financeInvoiceDetailHistory.setCreator(user.getUserID());
        financeInvoiceDetailHistory.setCreateDate(new Date());
        if ("1".equals(stateOld)){
            financeInvoiceDetailHistory.setOperation("1");  //1-增(开票录入),2-删,3-改,4-作废,5-改为尚未开具(启用)
        }else if ("3".equals(stateOld)){
            financeInvoiceDetailHistory.setOperation("5");
        }
        if (financeInvoiceDetailHistoryLast!=null){
            financeInvoiceDetailHistory.setPreviousId(financeInvoiceDetailHistoryLast.getId());
            financeInvoiceDetailHistory.setVersionNo(financeInvoiceDetailHistoryLast.getVersionNo());
        }
        financeInvoiceDetailHistoryDao.save(financeInvoiceDetailHistory);

    }

    //查找最后的
    @Override
    public FinanceInvoiceDetailHistory getInvoiceDetailHistoryLast(Integer invoiceDetailId){
        Map<String,Object> map = new HashMap<>();
        String hql = "from FinanceInvoiceDetailHistory where id=(select max(id) from FinanceInvoiceDetailHistory where invoiceDetailId=:invoiceDetailId)";
        map.put("invoiceDetailId",invoiceDetailId);
        return (FinanceInvoiceDetailHistory) financeInvoiceDetailHistoryDao.getByHQLWithNamedParams(hql,map);
    }

    @Override
    public void getEnable(Integer invoiceDetailId,User user) {
        FinanceInvoiceDetail financeInvoiceDetail = financeInvoiceDetailDao.get(invoiceDetailId);
        String stateOld = financeInvoiceDetail.getState();
        financeInvoiceDetail.setReason("");  // 清空
        financeInvoiceDetail.setState("4");  //启用
        financeInvoiceDetail.setUpdator(user.getUserID());
        financeInvoiceDetail.setUpdateName(user.getUserName());
        financeInvoiceDetail.setUpdateDate(new Date());

        FinanceInvoiceDetailHistory financeInvoiceDetailHistoryLast = getInvoiceDetailHistoryLast(financeInvoiceDetail.getId()); //获取上次修改
        FinanceInvoiceDetailHistory financeInvoiceDetailHistory = new FinanceInvoiceDetailHistory();
        BeanUtils.copyProperties(financeInvoiceDetail,financeInvoiceDetailHistory);
        financeInvoiceDetailHistory.setInvoiceDetail(financeInvoiceDetail);
        financeInvoiceDetailHistory.setInvoiceDetailId(financeInvoiceDetail.getId());
        financeInvoiceDetailHistory.setCreateName(user.getUserName());
        financeInvoiceDetailHistory.setCreator(user.getUserID());
        financeInvoiceDetailHistory.setCreateDate(new Date());
        if ("1".equals(stateOld)){
            financeInvoiceDetailHistory.setOperation("1");  //1-增(开票录入),2-删,3-改,4-作废,5-改为尚未开具(启用)
        }else if ("3".equals(stateOld)){
            financeInvoiceDetailHistory.setOperation("5");
        }
        financeInvoiceDetailHistory.setVersionNo("0");
        if (financeInvoiceDetailHistoryLast!=null){
            financeInvoiceDetailHistory.setPreviousId(financeInvoiceDetailHistoryLast.getId());
            financeInvoiceDetailHistory.setVersionNo(financeInvoiceDetailHistoryLast.getVersionNo()==null?"0":financeInvoiceDetailHistoryLast.getVersionNo()+1);
        }
        financeInvoiceDetailHistoryDao.save(financeInvoiceDetailHistory);
    }

    @Override
    public void cancelInvoice(Integer invoiceDetailId,String reason,String state,User user) {
        FinanceInvoiceDetail financeInvoiceDetail = financeInvoiceDetailDao.get(invoiceDetailId);
        if (financeInvoiceDetail!=null){
            financeInvoiceDetail.setState(state);
            financeInvoiceDetail.setReason(reason);
            financeInvoiceDetail.setUpdator(user.getUserID());
            financeInvoiceDetail.setUpdateName(user.getUserName());
            financeInvoiceDetail.setUpdateDate(new Date());

            FinanceInvoiceDetailHistory financeInvoiceDetailHistoryLast = getInvoiceDetailHistoryLast(financeInvoiceDetail.getId()); //获取上次修改
            FinanceInvoiceDetailHistory financeInvoiceDetailHistory = new FinanceInvoiceDetailHistory();
            BeanUtils.copyProperties(financeInvoiceDetail,financeInvoiceDetailHistory);
            financeInvoiceDetailHistory.setInvoiceDetail(financeInvoiceDetail);
            financeInvoiceDetailHistory.setInvoiceDetailId(financeInvoiceDetail.getId());
            financeInvoiceDetailHistory.setCreateName(user.getUserName());
            financeInvoiceDetailHistory.setCreator(user.getUserID());
            financeInvoiceDetailHistory.setCreateDate(new Date());
            financeInvoiceDetailHistory.setOperation("4");  //1-增(开票录入),2-删,3-改,4-作废,5-改为尚未开具(启用)
            if (financeInvoiceDetailHistoryLast!=null){
                financeInvoiceDetailHistory.setPreviousId(financeInvoiceDetailHistoryLast.getId());
                financeInvoiceDetailHistory.setVersionNo(financeInvoiceDetailHistoryLast.getVersionNo()==null?"0":financeInvoiceDetailHistoryLast.getVersionNo()+1);
            }
            financeInvoiceDetailHistoryDao.save(financeInvoiceDetailHistory);
        }
    }

    @Override
    public Map<String,Object> addInvoiceDetail(FinanceInvoiceDetail financeInvoiceDetail,User user,Map<String,Object> map) {
        FinanceInvoiceDetail fOld = getFinanceInvoiceDetailById(financeInvoiceDetail.getId());
        FinanceInvoiceSetting financeInvoiceSetting = getFinanceInvoiceSetting(user.getOid(),null,null);
        if (financeInvoiceDetail.getAmount()!=null && financeInvoiceSetting.getAmountLimited()!=null && financeInvoiceDetail.getAmount().doubleValue()<=financeInvoiceSetting.getAmountLimited()){
            fOld.setOperateDate(financeInvoiceDetail.getOperateDate());  //开票时间
            if (fOld.getOperateDate()!=null){
                fOld.setPeroid(NewDateUtils.getYearMonth(fOld.getOperateDate()));  //统计期
            }
            if (financeInvoiceDetail.getApplicationTime()!=null) {
                fOld.setApplicationTime(NewDateUtils.today(financeInvoiceDetail.getApplicationTime()));  //开票申请时间
            }
            fOld.setApplicantName(financeInvoiceDetail.getApplicantName());  //申请者
            fOld.setReceiveCorp(financeInvoiceDetail.getReceiveCorp());  //客户名称
            fOld.setReceiveCorpId(financeInvoiceDetail.getReceiveCorpId());  //客户id
            fOld.setAmount(financeInvoiceDetail.getAmount());
            fOld.setState("2");
            fOld.setTaxInclusive(financeInvoiceDetail.getTaxInclusive());  //是否含税
            fOld.setOperation("1");
            fOld.setUpdator(user.getUserID());
            fOld.setUpdateName(user.getUserName());
            fOld.setUpdateDate(new Date());
            fOld.setSource("1");
            financeInvoiceDetailDao.update(fOld);

            FinanceInvoiceDetailHistory financeInvoiceDetailHistoryLast = getInvoiceDetailHistoryLast(financeInvoiceDetail.getId()); //获取上次修改

            FinanceInvoiceDetailHistory financeInvoiceDetailHistory = new FinanceInvoiceDetailHistory();
            BeanUtils.copyProperties(financeInvoiceDetail,financeInvoiceDetailHistory);
            financeInvoiceDetailHistory.setInvoiceDetail(financeInvoiceDetail);
            financeInvoiceDetailHistory.setInvoiceDetailId(financeInvoiceDetail.getId());
            financeInvoiceDetailHistory.setCreateName(user.getUserName());
            financeInvoiceDetailHistory.setCreator(user.getUserID());
            financeInvoiceDetailHistory.setCreateDate(new Date());
            financeInvoiceDetailHistory.setOperation("1");  //1-增(开票录入),2-删,3-改,4-作废,5-改为尚未开具(启用)
            financeInvoiceDetailHistory.setState("2");
            financeInvoiceDetailHistory.setVersionNo("0");
            if (financeInvoiceDetailHistoryLast!=null){
                financeInvoiceDetailHistory.setPreviousId(financeInvoiceDetailHistoryLast.getId());
                financeInvoiceDetailHistory.setVersionNo(financeInvoiceDetailHistoryLast.getVersionNo()==null?"0":financeInvoiceDetailHistoryLast.getVersionNo()+1);
            }
            financeInvoiceDetailHistoryDao.save(financeInvoiceDetailHistory);
            map.put("state",1);  //成功
        }else {
            map.put("state",2);  //金额大于发票设置的金额上限
        }
        return map;
    }

    @Override
    public Map<String, Object> addInvoice(FinanceInvoice financeInvoice, User user,Map<String,Object> map) {
        if (financeInvoice!=null){
            financeInvoice.setCreator(user.getUserID());
            financeInvoice.setCreateName(user.getUserName());
            financeInvoice.setCreateDate(new Date());
            financeInvoice.setOrg(user.getOrganization());
            financeInvoice.setOrg_(user.getOid());
            financeInvoice.setBuyDate(NewDateUtils.today(financeInvoice.getBuyDate()));

            String beginNo1 = financeInvoice.getBeginNo();
            String beginNo2 = "";
            String endNo1 = financeInvoice.getEndNo();
            if (beginNo1.substring(0,1).equals("0")){
                for (int i=0;i<beginNo1.length();i++){
                    if (beginNo1.substring(i,i+1).equals("0")){
                        beginNo2 = beginNo1.substring(0,i+1).trim();
                    }else {
                        break;
                    }
                }
            }
            Integer beginNo = Integer.parseInt(beginNo1);
            Integer endNo = Integer.parseInt(endNo1);
            Integer cha = endNo-beginNo;
            if (cha >=0 && cha <=19){
                financeInvoiceDao.save(financeInvoice);

                for (int i=beginNo;i<=endNo;i++){
                    FinanceInvoiceDetail financeInvoiceDetail = new FinanceInvoiceDetail();
                    financeInvoiceDetail.setCreator(user.getUserID());
                    financeInvoiceDetail.setCreateName(user.getUserName());
                    financeInvoiceDetail.setCreateDate(new Date());
                    financeInvoiceDetail.setState("1");   //空或1-未使用,2-已使用,3-作废
                    financeInvoiceDetail.setOperation("1");
                    financeInvoiceDetail.setType(financeInvoice.getType());
                    String invoiceNo = beginNo2+String.valueOf(i); //发票号码长度固定为8
                    financeInvoiceDetail.setInvoiceNo(invoiceNo.length()>8?invoiceNo.substring(invoiceNo.length()-8,invoiceNo.length()):invoiceNo);
                    financeInvoiceDetail.setInvoiceReg(financeInvoice);
                    financeInvoiceDetail.setTeminateLabel(false);  //销售终止标识,true-终止  0-false 1-true
                    financeInvoiceDetail.setOrg(user.getOid());
                    financeInvoiceDetailDao.save(financeInvoiceDetail);
                }
                map.put("state",1);  //添加成功
            }else {
                map.put("state",2);  //发票多余20张或者少于1张
            }
        }else {
            map.put("state",0);   //添加失败
        }
        return map;
    }

    /**
     *
     * @param invoiceDetail 发票详情
     * @param operation  1-之前操作失误，该发票实际尚未开具 2-修改已开发票里面的内容 3-作废这张发票
     * @param map 修改状态
     * @param user  操作人
     * @return
     */
    @Override
    public Map<String, Object> updateInvoiceDetail(FinanceInvoiceDetail invoiceDetail, String operation, Map<String, Object> map,User user) {
        if (invoiceDetail!=null && !MyStrings.nulltoempty(operation).isEmpty()){
            FinanceInvoiceDetail financeInvoiceDetail = financeInvoiceDetailDao.get(invoiceDetail.getId());
            financeInvoiceDetail.setUpdateDate(new Date());
            financeInvoiceDetail.setUpdateName(user.getUserName());
            financeInvoiceDetail.setUpdator(user.getUserID());

            if (!"2".equals(operation)) {
                financeInvoiceDetail.setAmount(new BigDecimal(0));
                financeInvoiceDetail.setOperateDate(null);
                financeInvoiceDetail.setReceiveCorp("");
                financeInvoiceDetail.setApplicationTime(null);
                financeInvoiceDetail.setTaxInclusive("1");
                financeInvoiceDetail.setApplicantName("");
                financeInvoiceDetail.setReason("");
                if ("1".equals(operation)){
                    financeInvoiceDetail.setState("4"); // 启用
                    financeInvoiceDetail.setOperation("5");
                } else if ("3".equals(operation)){
                    financeInvoiceDetail.setState("3");  //作废
                    financeInvoiceDetail.setOperation("4"); //作废
                }
            }else if ("2".equals(operation)){  //修改内容
                financeInvoiceDetail.setAmount(invoiceDetail.getAmount());
                financeInvoiceDetail.setOperateDate(invoiceDetail.getOperateDate());
                financeInvoiceDetail.setReceiveCorp(invoiceDetail.getReceiveCorp());
                financeInvoiceDetail.setApplicationTime(invoiceDetail.getApplicationTime());
                financeInvoiceDetail.setTaxInclusive(invoiceDetail.getTaxInclusive());
                financeInvoiceDetail.setApplicantName(invoiceDetail.getApplicantName());
                financeInvoiceDetail.setVersionNo(invoiceDetail.getVersionNo());
                if(invoiceDetail.getLosted()!=null){
                    financeInvoiceDetail.setLosted(invoiceDetail.getLosted());
                }
            }

            FinanceInvoiceDetailHistory financeInvoiceDetailHistoryLast = getInvoiceDetailHistoryLast(financeInvoiceDetail.getId()); //获取上次修改

            FinanceInvoiceDetailHistory financeInvoiceDetailHistory = new FinanceInvoiceDetailHistory();
            BeanUtils.copyProperties(financeInvoiceDetail,financeInvoiceDetailHistory);
            financeInvoiceDetailHistory.setInvoiceDetail(financeInvoiceDetail);
            financeInvoiceDetailHistory.setInvoiceDetailId(financeInvoiceDetail.getId());
            financeInvoiceDetailHistory.setCreateName(user.getUserName());
            financeInvoiceDetailHistory.setCreator(user.getUserID());
            financeInvoiceDetailHistory.setCreateDate(new Date());
            if ("1".equals(operation)){
                financeInvoiceDetailHistory.setOperation("3");  //1-增(开票录入),2-删,3-改,4-作废,5-改为尚未开具(启用)
            }else if ("2".equals(operation)){
                financeInvoiceDetailHistory.setOperation("3");  //1-增(开票录入),2-删,3-改,4-作废,5-改为尚未开具(启用)
            }else if ("3".equals(operation)){
                financeInvoiceDetailHistory.setOperation("4");  //1-增(开票录入),2-删,3-改,4-作废,5-改为尚未开具(启用)
            }
            financeInvoiceDetailHistory.setVersionNo("0");
            if (financeInvoiceDetailHistoryLast!=null){
                financeInvoiceDetailHistory.setPreviousId(financeInvoiceDetailHistoryLast.getId());
                financeInvoiceDetailHistory.setVersionNo(financeInvoiceDetailHistoryLast.getVersionNo()==null?"0":financeInvoiceDetailHistoryLast.getVersionNo()+1);
            }
            financeInvoiceDetailHistoryDao.save(financeInvoiceDetailHistory);

            map.put("state",1);  //成功

        }else {
            map.put("state",0);  //失败
        }
        return map;
    }

    @Override
    public Map<String, Object> getRecords(Integer invoiceDetailId, Map<String, Object> map) {
        List<FinanceInvoiceDetailHistory> financeInvoiceDetailHistories = getInvoiceDetailHistoryByDetailId(invoiceDetailId);
        for (FinanceInvoiceDetailHistory f:financeInvoiceDetailHistories) {
            if (f.getPreviousId()!=null) {
                FinanceInvoiceDetailHistory financeInvoiceDetailHistory = financeInvoiceDetailHistoryDao.get(f.getPreviousId());
                f.setPreviousState(financeInvoiceDetailHistory.getState());
            }else {
                f.setPreviousState("1");  //空白
            }
        }
        map.put("financeInvoiceDetailHistories",financeInvoiceDetailHistories);
        return map;
    }

    private List<FinanceInvoiceDetailHistory> getInvoiceDetailHistoryByDetailId(Integer invoiceDetailId){
        Map<String,Object> params = new HashMap<>();
        String hql = "from FinanceInvoiceDetailHistory where invoiceDetailId=:invoiceDetailId";
        params.put("invoiceDetailId",invoiceDetailId);
        return financeInvoiceDetailHistoryDao.getListByHQLWithNamedParams(hql,params);
    }

    @Override
    public FinanceInvoiceDetailHistory getInvoiceDetailHistoryDetail(Integer invoiceDetailHistoryId) {
        return financeInvoiceDetailHistoryDao.get(invoiceDetailHistoryId);
    }

    @Override
    public Map<String, Object> getRemind(Integer oid,User user,Map<String,Object> map) {
        List<FinanceInvoiceSetting> financeInvoiceSettings = getFinanceInvoiceSettingByOid(oid);
        LocalSetting localSetting = getLocalSettingByOrg(oid);
        if (localSetting==null){
            localSetting = new LocalSetting();
            localSetting.setCreateDate(new Date());
            localSetting.setOrg(oid);
            if (user!=null) {
                localSetting.setCreateName(user.getUserName());
                localSetting.setCreator(user.getUserID());
            }
            localSetting.setKey("开票申请提示");
            localSetting.setValue("1");
            localSetting.setEnabled(true); //需提醒
            localSettingDao.save(localSetting);
        }
        if (localSetting.isEnabled()) {  //需要提醒
            if (financeInvoiceSettings.size() > 0) {
                map.put("state", 1);   //有发票设置
            } else {
                map.put("state", 0);  //没有进行发票设置
            }
        }else {
            map.put("state", 2);  //不需提醒
        }
        map.put("financeInvoiceSettings", financeInvoiceSettings);
        return map;
    }

    private LocalSetting getLocalSettingByOrg(Integer oid){
        Map<String,Object> params = new HashMap<>();
        String hql = "from LocalSetting where org=:org and key='开票申请提示'";
        params.put("org",oid);
        return (LocalSetting) localSettingDao.getByHQLWithNamedParams(hql,params);
    }

    @Override
    public Map<String, Object> updateEnbale(Integer oid,Boolean enable,Map<String, Object> map) {
        LocalSetting localSetting = getLocalSettingByOrg(oid);
        if (!enable){
            localSetting.setEnabled(enable);
        }
        map.put("state",1);
        return map;
    }

    @Override
    public Integer getInvoiceSettingNumByOid(Integer oid) {
        Map<String,Object> params = new HashMap<>();
        String hql = "select count(id) from FinanceInvoiceSetting where org_=:oid ";
        params.put("oid",oid);
        Long num = (Long) financeInvoiceSettingDao.getByHQLWithNamedParams(hql,params);
        return num==null?0:num.intValue();
    }

    /**
     * 销售终止申请时对票据的操作
     * @param invoiceDetailId  //发票详情id
     */
    @Override
    public void terminationApply(Integer invoiceDetailId) {
        if (invoiceDetailId!=null) {
            FinanceInvoiceDetail financeInvoiceDetail = financeInvoiceDetailDao.get(invoiceDetailId);
            if (financeInvoiceDetail != null) {
                financeInvoiceDetail.setTeminateLabel(true);  //销售终止标识,true-终止  0-false 1-true
                financeInvoiceDetail.setTeminateTime(new Date());
                financeInvoiceDetailDao.update(financeInvoiceDetail);
            }
        }
    }

    @Override
    public FinanceInvoice getInvoiceById(Integer invoiceId) {
        return financeInvoiceDao.get(invoiceId);
    }

    @Override
    public Integer getBlackInvoiceNum(Integer oid,String type) {
        Map<String,Object> map = new HashMap<>();
        String hql = "select count(id) from FinanceInvoiceDetail where state='1' and invoiceReg_ in (select id from FinanceInvoice where org_=:org)";
        map.put("org",oid);
        if (!MyStrings.nulltoempty(type).isEmpty()){
            hql+=" and type=:type";    //1-增值税专用票,2-增值税普通票,3-其它普通票
            map.put("type",type);
        }
        Long num = (Long) financeInvoiceDetailDao.getByHQLWithNamedParams(hql,map);
        return num.intValue();
    }

    @Override
    public Integer getInvoiceNum(Integer oid, Integer year) {
        Map<String,Object> map = new HashMap<>();
        String hql = "select count(id) from FinanceInvoice where org_=:org";
        map.put("org",oid);
        if (year!=null){
            hql+=" and YEAR(createDate)=:year";
            map.put("year",year);
        }
        Long num = (Long) financeInvoiceDao.getByHQLWithNamedParams(hql,map);
        return num.intValue();
    }

    @Override
    public Map<String,Object> getBlackInvoiceDetails(Integer oid, String type, String state) {
        Map<String,Object> map = new HashMap<>();
        String hql = "select fid.id,fid.invoiceNo,fi.buyerName,fi.buyDate from FinanceInvoiceDetail fid,FinanceInvoice fi where " +
                "fi.id=fid.invoiceReg_ and fi.org_=:org";
        map.put("org",oid);
        if (!MyStrings.nulltoempty(type).isEmpty()){
            hql+=" and fid.type=:type";    //1-增值税专用票,2-增值税普通票,3-其它普通票
            map.put("type",type);
        }
        if (!MyStrings.nulltoempty(state).isEmpty()){
            hql+=" and fid.state=:state";    //状态:1-空白,2-已使用,3-作废,4-启用
            map.put("state",state);
        }
        List<Object[]> objects = financeInvoiceDetailDao.getListByHQLWithNamedParams(hql,map);
        map.clear();
        List<Map<String,Object>> details = new ArrayList<>();
        for (Object[] ob:objects) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("invoiceDetailId",ob[0]);  //发票详情id
            map1.put("invoiceNo",ob[1]);  //发票号
            map1.put("buyerName",ob[2]);  //购买者
            map1.put("buyDate",ob[3]);  //购买时间
            details.add(map1);
        }
        map.put("invoiceDetails",details);
        return map;
    }

    @Override
    public Map getinvoices(Integer oid,Integer customerId, String startTime, String endTime, String rule, String orderBy) {
        String sql = "SELECT sc.id as customer_id,sc.name,sc.full_name,ia.id,sum(iai.amount) as amount FROM t_sl_invoice_application ia LEFT JOIN t_sl_invoice_application_item iai ON iai.application = ia.id LEFT JOIN t_sl_orders so ON ia.orders = so.id LEFT JOIN t_sl_customer sc ON so.customer = sc.id WHERE ia.state IN (5, 6, 7, 8, 9) ";

        sql+= " and so.oid=?0 ";
        if(startTime!=null){
            sql+=" and ia.create_date between '"+startTime+"' and '"+endTime+"'";
        }
        if(customerId!=null)
            sql +=" and sc.id = "+customerId;
        sql+=" GROUP BY sc.id";

        if (orderBy!=null)
            sql+= " ORDER BY "+orderBy+" "+ (StringUtils.isNotBlank(rule)?rule:"desc");

        Map map = financeInvoiceDao.findMapByConditionNoPage(sql,new Object[]{oid});

        BigDecimal allOuts = new BigDecimal("0");
        ArrayList<HashMap> list = (ArrayList) map.get("data");

        if (list != null)
            for (HashMap m : list) {
                allOuts = allOuts .add((BigDecimal) m.get("amount"));
            }
        map.put("all_invoice",allOuts);
        return map;
    }

    @Override   //dateType 1-按年查 2-按年月
    public Map<String, Object> getInvoiceUsed(Integer oid,Date beginDate, String state,String type,Integer dateType) {
        Map<String,Object> map = new HashMap<>();
        String hql = "select count(id),sum(amount) from FinanceInvoiceDetail where invoiceReg_ in (select id from FinanceInvoice where org_=:org";
        map.put("org",oid);
        if (StringUtils.isNotEmpty(type)){
            hql+=" and type=:type)";    //1-增值税专用票,2-增值税普通票,3-其它普通票
            map.put("type",type);
        }else {
            hql+=")";  //如果没有type，则直接添加后面的半括号
        }
        if (StringUtils.isNotEmpty(state)){
            hql+=" and state=:state";    //1-空白,2-已使用,3-作废,4-启用
            map.put("state",state);
        }
        if (beginDate!=null&dateType!=null){
            if (1==dateType) {
                hql += " and YEAR(operateDate)=YEAR(:beginDate) ";  //开票日期
            }else if (2==dateType){
                hql += " and DATE_FORMAT(operateDate,'%Y%m')=DATE_FORMAT(:beginDate,'%Y%m') ";
            }
            map.put("beginDate",beginDate);
        }
        Object[] aa = (Object[]) financeInvoiceDetailDao.getByHQLWithNamedParams(hql,map);
        map.clear();
        if (aa.length>0) {
            map.put("num", aa[0]);
            map.put("totalAmount", aa[1]);
        }
        return map;
    }

//    @Override   //dateType 1-按年查 2-按年月
//    public List<FinanceInvoiceDetail> getInvoiceIdUsed(Integer oid,Date beginDate, String state,String type,Integer dateType) {
//        Map<String,Object> map = new HashMap<>();
//        String hql = "from FinanceInvoiceDetail where invoiceReg_ in (select id from FinanceInvoice where org_=:org";
//        map.put("org",oid);
//        if (StringUtils.isNotEmpty(type)){
//            hql+=" and type=:type)";    //1-增值税专用票,2-增值税普通票,3-其它普通票
//            map.put("type",type);
//        }else {
//            hql+=")";  //如果没有type，则直接添加后面的半括号
//        }
//        if (StringUtils.isNotEmpty(state)){
//            hql+=" and state=:state";    //1-空白,2-已使用,3-作废,4-启用
//            map.put("state",state);
//        }
//        if (beginDate!=null&dateType!=null){
//            if (1==dateType) {
//                hql += " and YEAR(operateDate)=YEAR(:beginDate) ";  //开票日期
//            }else if (2==dateType){
//                hql += " and DATE_FORMAT(operateDate,'%Y%m')=DATE_FORMAT(:beginDate,'%Y%m') ";
//            }
//            map.put("beginDate",beginDate);
//        }
//        List<FinanceInvoiceDetail> financeInvoiceDetails = financeInvoiceDetailDao.getListByHQLWithNamedParams(hql,map);
////        Object[] aa = (Object[]) financeInvoiceDetailDao.getByHQLWithNamedParams(hql,map);
////        map.clear();
////        if (aa.length>0) {
////            map.put("num", aa[0]);
////            map.put("totalAmount", aa[1]);
////        }
//        return financeInvoiceDetails;
//    }

    @Override
    public List<Map> getInvoiceMonthUsed(Integer oid, Date beginTime, Date endTime,String state, String type, Integer dateType) {
        Integer year = NewDateUtils.getYear(beginTime);
        Integer monthBegin = NewDateUtils.getMonth(beginTime);
        Integer monthEnd = NewDateUtils.getMonth(endTime);
        List<Map> mapList = new ArrayList<>();
        while (monthBegin<=monthEnd){
            Map<String,Object> map1 = new HashMap<>();
            String monthYear = year+"-"+monthBegin;
            if (monthBegin<10){
                monthYear = year+"-0"+monthBegin;
            }
            Map<String,Object> allInvoice = getInvoiceUsed(oid,beginTime,state,type,dateType);  //所有的
            Long num = (Long) allInvoice.get("num");
            if (num!=0){
                map1.put("monthYear",monthYear);
                map1.put("allInvoice",allInvoice);
                mapList.add(map1);
            }
            monthBegin++;
            beginTime = NewDateUtils.changeMonth(beginTime,1);
        }
        return mapList;
    }

    @Override
    public void deleteUpdateById(Integer invoiceDetailId) {
        FinanceInvoiceDetail invoiceDetail = financeInvoiceDetailDao.get(invoiceDetailId);//获取发票明细
        //TODO 对删除的发票清空

        if (invoiceDetail!=null) {
            invoiceDetail.setPeroid(null); //统计期（所属年月）
            invoiceDetail.setState("1"); //状态:1-空白,2-已使用,3-作废,4-启用
            invoiceDetail.setAmount(null);
            invoiceDetail.setOperateDate(null);
            invoiceDetail.setLines(null);
            invoiceDetail.setReceiveCorpId(null);   //客户id
            invoiceDetail.setReceiveCorp(null); //客户名称
            invoiceDetail.setApplicantName(null);  //申请人
            invoiceDetail.setApplicant(null);  //申请人id
            invoiceDetail.setApplicationTime(null);  //申请时间
            invoiceDetail.setSource(null);  //来源:1-财务录入,2-销售
            invoiceDetail.setUpdator(null);
            invoiceDetail.setUpdateName(null);
            invoiceDetail.setUpdateDate(null);
            financeInvoiceDetailDao.update(invoiceDetail);

            FinanceInvoiceDetailHistory financeInvoiceDetailHistoryLast = getInvoiceDetailHistoryLast(invoiceDetail.getId()); //获取上次修改
            if (financeInvoiceDetailHistoryLast != null) {
                financeInvoiceDetailHistoryDao.delete(financeInvoiceDetailHistoryLast);
            }
        }
    }

    @Override
    public void saveOrUpdateInvoiceDetail(SlInvoiceApplicationDetail detail) {
        invoiceApplicationDetailDao.saveOrUpdate(detail);
    }

    @Override
    public void deleteInvoiceRateHistory(FinanceInvoiceRateHistory financeInvoiceRateHistory) {
        financeInvoiceRateHistoryDao.delete(financeInvoiceRateHistory);
    }
}
