package cn.sphd.miners.modules.commodity.service.impl;

import cn.sphd.miners.modules.commodity.entity.PdCommodityMedia;
import cn.sphd.miners.modules.commodity.entity.PdCommodityMediaHistory;

import cn.sphd.miners.modules.commodity.service.ProductImageService;

import cn.sphd.miners.modules.uploads.service.FileUsingCallback;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;

public class ProductUsing implements FileUsingCallback {
    private static final long serialVersionUID = 1L;
    Integer id;
    String entityClass;
    @Override
    public boolean checkUsing(String filename) {
        if(StringUtils.isNotEmpty(filename)) {
            ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
            ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
            ProductImageService service = ac.getBean(ProductImageService.class, "productImageService");

            switch (entityClass) {
                case "PdCommodityMedia":
                    PdCommodityMedia entity = service.getPdCommodityMedia(id);
                    if (entity != null&&service.getProduct(entity.getCommodity())!=null) {
                        return filename.equals(entity.getUplaodPath());
                    }
                    break;
                case "PdCommodityMediaHistory":

                    PdCommodityMediaHistory historyEntity = service.getPdCommodityMediaHistory(id);
                    if(historyEntity != null) {
                        PdCommodityMedia sb = service.getPdCommodityMedia(historyEntity.getCommodityMedia());
                        if(sb!=null && service.getProduct(sb.getCommodity())!=null){
                            return  filename.equals(historyEntity.getUplaodPath());
                        }
                    }
                    break;

            }
        }
        return false;
    }

    @Override
    @JsonIgnore @JSONField(serialize = false)
    public String getKey() {
        /* 由于本回调类是多实体使用，需要使用id+实体类名称作为唯一key；
         * 被UploadFileUsing.callbackKey保存数据库用于删除文件引用。 */
        return id+entityClass;
    }

    public ProductUsing(Integer id, Class entityClass) {
        this.id = id;
        String className = entityClass.getName();
        //实体类判断，去掉包路径，方便后续包改名或者改路径无需修改代码。
        this.entityClass = className.substring(className.lastIndexOf('.')+1);
    }


    public Integer getId() {
        return id;
    }

    public String getEntityClass() {
        return entityClass;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public void setEntityClass(String entityClass) {
        this.entityClass = entityClass;
    }

    public ProductUsing() {
    }
}
