package cn.sphd.miners.modules.commodity.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.accountant.util.DateUtil;
import cn.sphd.miners.modules.commodity.dao.*;
import cn.sphd.miners.modules.commodity.entity.*;
import cn.sphd.miners.modules.commodity.service.PdPackagingService;
import cn.sphd.miners.modules.material.dao.MtBaseDao;
import cn.sphd.miners.modules.material.dao.MtBaseHistoryDao;
import cn.sphd.miners.modules.material.dao.MtCategoryDao;
import cn.sphd.miners.modules.material.entity.MtBase;
import cn.sphd.miners.modules.material.entity.MtBaseHistory;
import cn.sphd.miners.modules.material.entity.MtCategory;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class PdPackagingServiceImpl implements PdPackagingService {

    @Autowired
    private PdMerchandiseDao pdMerchandiseDao;

    @Autowired
    private MtCategoryDao mtCategoryDao;

    @Autowired
    private MtBaseDao mtBaseDao;

    @Autowired
    private PdBaseDao pdBaseDao;

    @Autowired
    private PdPackagingInfoDao pdPackagingInfoDao;

    @Autowired
    private PdPackagingItemDao pdPackagingItemDao;

    @Autowired
    private PdPackagingItemHistoryDao pdPackagingItemHistoryDao;

    @Autowired
    private PdPackagingStructureDao pdPackagingStructureDao;

    @Autowired
    private PdPackagingStructureHistoryDao pdPackagingStructureHistoryDao;

    @Autowired
    private PdPackagingInfoHistoryDao pdPackagingInfoHistoryDao;

    @Autowired
    private MtBaseHistoryDao mtBaseHistoryDao;

    /**
     * @param oid
     * @param keyword
     * @param type     1 已设置 0未设置
     * @param pageInfo
     * @return
     */
    @Override
    public List<Map<String, Object>> getPackagingList(Integer oid, String keyword, int type, PageInfo pageInfo) {

        String hql = "SELECT new Map( " +
                "p.id AS id,\n" +
                "p.outerSn AS outerSn,\n" +
                "p.outerName as outerName,\n" +
                "p.model as model,\n" +
                "p.specifications as specifications,\n" +
                "p.unit as unit,\n" +
                "p.unitId as unitId," +
                "p.packagingState as packagingState," +
                "p.createName as createName,\n" +
                "p.createDate as createDate,\n" +
                "pb.netWeight as netWeight,\n" +
                "pb.weightUnit as weightUnit,\n" +
                "c.name as customerName," +
                "(SELECT COUNT(i.id) FROM PdPackagingInfo i WHERE i.merchandise = p.id and i.layersCount != 0 and i.enabled=1 ) as num) from PdMerchandise p " +
                "left join SlCustomer c on p.customer =c.id " +
                "left join PdBase pb on p.product =pb.id " +
                "where " +
                "p.org=:org and p.enabled=1 ";
        if (type == 1) {
            hql += " and  p.packagingState = 2";
        } else {
            hql += " and  ( p.packagingState != 2 or  p.packagingState is null) ";
        }
        Map param = new HashMap();
        param.put("org", oid);
        return pdMerchandiseDao.getListByHQLWithNamedParams(hql, param, pageInfo);
    }

    @Override
    public Object getWszNum(Integer oid) {
        String hql = "SELECT COUNT(0) as num from PdMerchandise p" +
                " left join PdBase pb on p.product =pb.id where " +
                "p.org=:org and  p.enabled=1 and (  p.packagingState != 2  or p.packagingState  is null )";
        Map param = new HashMap();
        param.put("org", oid);
        return pdMerchandiseDao.getByHQLWithNamedParams(hql, param);
    }

    @Override
    public Integer getBzTypeId(Integer oid) {
        String hql = "from MtCategory where org_=:org and level=1 and name = '商品的包装物'";
        Map param = new HashMap();
        param.put("org", oid);
        MtCategory mtCategory = (MtCategory) mtCategoryDao.getByHQLWithNamedParams(hql, param);
        if (mtCategory != null) {
            return mtCategory.getId();
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> getMtBaseByType(Integer oid, Integer typeId) {
        String hql = "select new Map(id as id,code as code,name as name,memo as memo,model as model,specifications as specifications,unitId as unitId," +
                "unit as unit) from MtBase where org=:org and category_=:category and enabled = '1'";
        Map param = new HashMap();
        param.put("org", oid);
        param.put("category", typeId);

        return mtBaseDao.getListByHQLWithNamedParams(hql, param);
    }

    @Override
    public void deletePackaging(Integer id) {
        pdPackagingInfoDao.deleteById(id);

        List<PdPackagingInfoHistory> allList = pdPackagingInfoDao.getListByHQLWithNamedParams("from PdPackagingInfoHistory where packaging=" + id, new HashMap<>());

        pdPackagingInfoHistoryDao.deleteAll(allList);


    }

    @Override
    public String addPackaging(PdPackagingInfo info, User user) {

        Integer productId = info.getProductId();
        List<PdPackagingStructure> pdPackagingStructureList = JSONObject.parseArray(info.getList(), PdPackagingStructure.class);


        if (pdPackagingStructureList == null) {
            pdPackagingStructureList = new ArrayList<>();
        }
        PdMerchandise pdMerchandise = pdMerchandiseDao.get(productId);
        if (pdMerchandise == null) {
            return "商品信息不存在";
        }
        PdBase pdBase = new PdBase();

        if (pdMerchandise.getProduct_() == null) {
            pdBase.setNetWeight(new BigDecimal(0));
            pdBase.setWeightUnit("1");
        } else {
            pdBase = pdBaseDao.get(pdMerchandise.getProduct_());
            if (pdBase == null) {
                pdBase = new PdBase();
                pdBase.setNetWeight(new BigDecimal(0));
                pdBase.setWeightUnit("1");

            }
        }


        if (pdBase.getNetWeight() == null) {
            pdBase.setNetWeight(new BigDecimal(0));
            pdBase.setWeightUnit("1");
        }

//        PdPackagingInfo info=new PdPackagingInfo();
        info.setOrg(user.getOid());
        info.setMerchandise(productId);
        info.setOuterSn(pdMerchandise.getOuterSn());
        info.setEnabledTime(new Date());
        info.setEnabled(1);
        info.setCreateDate(new Date());
        info.setCreateName(user.getUserName());
        info.setCreator(user.getUserID());
        info.setEffectTime(DateUtil.fomatDate(DateUtil.getDay()));
        info.setOperation(1);

        info.setLayersCount(pdPackagingStructureList.size());

        //无需包装，全部暂停
        if (pdPackagingStructureList.size() == 0) {
            Map<String, Object> p = new HashMap<>();
            p.put("merchandise", productId);
            List<PdPackagingInfo> infoList = pdPackagingInfoDao.getListByHQLWithNamedParams("from PdPackagingInfo where merchandise=:merchandise", p);

            if (infoList.size() > 0) {
                if (pdMerchandise.getPackagingState() == null || pdMerchandise.getPackagingState() == 0 || pdMerchandise.getPackagingState() == 1) {
                    for (PdPackagingInfo info1 : infoList) {
                        pdPackagingInfoDao.delete(info1);
                        pdPackagingInfoHistoryDao.queryHQLWithNamedParams("delete from PdPackagingInfoHistory where packaging =" + info1.getId(), new HashMap<>());
                    }
                } else {
                    for (PdPackagingInfo info1 : infoList) {
                        info1.setEnabled(0);
                        info1.setEnabledTime(new Date());
                        pdPackagingInfoDao.update(info1);
                    }
                    Map<String, Object> m2 = new HashMap<>();
                    m2.put("merchandise", productId);
                    List<PdPackagingInfoHistory> allList = pdPackagingInfoDao.getListByHQLWithNamedParams("from PdPackagingInfoHistory where id in (select max(sh.id) from PdPackagingInfoHistory as sh where sh.merchandise=:merchandise  and sh.effectTime <= current_timestamp() group by sh.packaging ) ", m2);

                    for (PdPackagingInfoHistory hi:allList ) {
                        hi.setEnabled(0);
                        hi.setEnabledTime(new Date());
                        pdPackagingInfoHistoryDao.update(hi);
                    }
                }
            }

        }else{
            //删除无需包装的数据
            List<PdPackagingInfo> wx = getWxPackagingList(productId);
            if(wx.size()>0){
                for (PdPackagingInfo w : wx) {
                    pdPackagingInfoDao.delete(w);
                    pdPackagingInfoHistoryDao.queryHQLWithNamedParams("delete from PdPackagingInfoHistory where packaging =" + w.getId(), new HashMap<>());
                }

            }

        }

        for (int i = 0; i < pdPackagingStructureList.size(); i++) {

            if (pdPackagingStructureList.get(i).getItemList() == null) {
                pdPackagingStructureList.get(i).setItemList(new ArrayList<>());
            }

            pdPackagingStructureList.get(i).setProduct(productId);
            pdPackagingStructureList.get(i).setLevel(i + 1);

            pdPackagingStructureList.get(i).setIsRoot(1);
            pdPackagingStructureList.get(i).setIsLeaf(0);
            pdPackagingStructureList.get(i).setPackagingCount(pdPackagingStructureList.get(i).getProductCount());
            pdPackagingStructureList.get(i).setLayersCount(pdPackagingStructureList.size());
            pdPackagingStructureList.get(i).setMaterialCount(pdPackagingStructureList.get(i).getItemList().size() + 1);
            pdPackagingStructureList.get(i).setEnabled(1);
            pdPackagingStructureList.get(i).setEnabledTime(info.getCreateDate());
            pdPackagingStructureList.get(i).setCreateDate(info.getCreateDate());
            pdPackagingStructureList.get(i).setCreateName(info.getCreateName());
            pdPackagingStructureList.get(i).setCreator(info.getCreator());


            if (i == 0) {
                // pdPackagingStructureList.get(i).setParent(0);
                //添加产品重量
                setNetWeight(pdPackagingStructureList.get(i), pdBase, null, null);
                //添加包装重量
                setGrossWeight(pdPackagingStructureList.get(i));
            } else {
                // pdPackagingStructureList.get(i).setParent(pdPackagingStructureList.get(i-1).getId());
                //添加产品重量
                setNetWeight(pdPackagingStructureList.get(i), pdBase, pdPackagingStructureList.get(i - 1).getNetWeight(), pdPackagingStructureList.get(i - 1).getNetUnitId());
                //添加包装重量
                setGrossWeight(pdPackagingStructureList.get(i));
            }
        }

        if (pdPackagingStructureList.size() > 0) {
            //计算产品净重
            info.setNetWeight(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getNetWeight());
            info.setNetUnitId(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getNetUnitId());
            info.setNetUnit(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getNetUnit());

            info.setGrossWeight(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getGrossWeight());
            info.setGrossUnitId(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getGrossUnitId());
            info.setGrossUnit(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getGrossUnit());

            info.setTotalWeight(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getTotalWeight());
            info.setTotalUnitId(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getTotalUnitId());
            info.setTotalUnit(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getTotalUnit());

            info.setProductCount(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getProductCount());
            info.setPackagingCount(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getPackagingCount());
        }

        pdPackagingInfoDao.save(info);
        PdPackagingInfoHistory infoHistory = pdPackagingInfoHistoryDao.insert(info);

        for (PdPackagingStructure pdPackagingStructure : pdPackagingStructureList) {
            pdPackagingStructure.setPackaging(info.getId());
            pdPackagingStructureDao.save(pdPackagingStructure);
            PdPackagingStructureHistory packagingStructureHistory = pdPackagingStructureHistoryDao.insert(pdPackagingStructure, infoHistory.getId());

            for (PdPackagingItem item : pdPackagingStructure.getItemList()
            ) {
                item.setPackagingStruct(pdPackagingStructure.getId());
                pdPackagingItemDao.save(item);

                MtBase mtBase = mtBaseDao.get(item.getMaterial());
                mtBase.setOperation(null);
                int baseHistoryId = mtBaseHistoryDao.insert(mtBase);
                pdPackagingItemHistoryDao.insert(item, packagingStructureHistory.getId(), baseHistoryId);
            }

            pdPackagingStructure.getZyPackaging().setPackagingStruct(pdPackagingStructure.getId());
            pdPackagingItemDao.save(pdPackagingStructure.getZyPackaging());

            MtBase mtBase = mtBaseDao.get(pdPackagingStructure.getZyPackaging().getMaterial());
            mtBase.setOperation(null);
            int baseHistoryId = mtBaseHistoryDao.insert(mtBase);
            pdPackagingItemHistoryDao.insert(pdPackagingStructure.getZyPackaging(), packagingStructureHistory.getId(), baseHistoryId);
        }


//        if (pdMerchandise.getPackagingState() == null || pdMerchandise.getPackagingState().intValue() == 0) {
//            pdMerchandise.setPackagingState(2);
//            pdMerchandiseDao.update(pdMerchandise);
//        }
        pdMerchandise.setPackagingState(2);
        pdMerchandiseDao.update(pdMerchandise);
        return "success";
    }

    @Override
    public String updatePackaging(PdPackagingInfo info, List<PdPackagingStructure> pdPackagingStructureList, User user) {


        Integer productId = info.getMerchandise();
        PdMerchandise pdMerchandise = pdMerchandiseDao.get(productId);
        if (pdMerchandise == null) {
            return "商品信息不存在";
        }

        PdBase pdBase = new PdBase();

        if (pdMerchandise.getProduct_() == null) {
            pdBase.setNetWeight(new BigDecimal(0));
            pdBase.setWeightUnit("1");
        } else {
            pdBase = pdBaseDao.get(pdMerchandise.getProduct_());
            if (pdBase == null) {
                pdBase = new PdBase();
                pdBase.setNetWeight(new BigDecimal(0));
                pdBase.setWeightUnit("1");

            }
        }

        if (pdBase.getNetWeight() == null) {
            pdBase.setNetWeight(new BigDecimal(0));
            pdBase.setWeightUnit("1");
        }


        //切断历史数据关联
        Map<String, Object> map = new HashMap<>();
        map.put("packaging", info.getId());
        List<PdPackagingStructure> structureList = pdPackagingStructureDao.getListByHQLWithNamedParams("from PdPackagingStructure where packaging=:packaging and enabled=1", map);
        pdPackagingStructureDao.deleteAll(structureList);

//        PdPackagingInfo info=new PdPackagingInfo();
        info.setOrg(user.getOid());
        info.setMerchandise(productId);
        info.setOuterSn(pdMerchandise.getOuterSn());
        info.setLayersCount(pdPackagingStructureList.size());

        //无需包装，删除所有的包装
        if (pdPackagingStructureList.size() == 0) {
            Map<String, Object> p = new HashMap<>();
            p.put("merchandise", productId);
            List<PdPackagingInfo> infoList = pdPackagingStructureDao.getListByHQLWithNamedParams("from PdPackagingInfo where merchandise=:merchandise", p);
            if (infoList.size() > 0) {
                if (pdMerchandise.getPackagingState() == null || pdMerchandise.getPackagingState() == 0 || pdMerchandise.getPackagingState() == 1) {


                    for (PdPackagingInfo info1 : infoList) {
                        pdPackagingInfoDao.delete(info1);
                        pdPackagingInfoHistoryDao.getByHQLWithNamedParams("delete from PdPackagingInfoHistory where packaging =" + info1.getId(), new HashMap<>());
                    }

                } else {
                    for (PdPackagingInfo info1 : infoList) {
                        info1.setEnabled(0);
                        info1.setEnabledTime(new Date());
                        pdPackagingInfoDao.update(info1);
                    }
                    Map<String, Object> m2 = new HashMap<>();
                    m2.put("merchandise", productId);
                    List<PdPackagingInfoHistory> allList = pdPackagingInfoDao.getListByHQLWithNamedParams("from PdPackagingInfoHistory where id in (select max(sh.id) from PdPackagingInfoHistory as sh where sh.merchandise=:merchandise  and sh.effectTime <= current_timestamp() group by sh.packaging ) ", m2);

                    for (PdPackagingInfoHistory hi:allList ) {
                        hi.setEnabled(0);
                        hi.setEnabledTime(new Date());
                        pdPackagingInfoHistoryDao.update(hi);
                    }
                }
            }
        }else{
            //删除无需包装

            List<PdPackagingInfo> wx = getWxPackagingList(productId);
            if(wx.size()>0){
                for (PdPackagingInfo w : wx) {
                    pdPackagingInfoDao.delete(w);
                    pdPackagingInfoHistoryDao.queryHQLWithNamedParams("delete from PdPackagingInfoHistory where packaging =" + w.getId(), new HashMap<>());
                }
            }
        }

        for (int i = 0; i < pdPackagingStructureList.size(); i++) {

            if (pdPackagingStructureList.get(i).getItemList() == null) {
                pdPackagingStructureList.get(i).setItemList(new ArrayList<>());
            }

            pdPackagingStructureList.get(i).setProduct(productId);
            pdPackagingStructureList.get(i).setLevel(i + 1);

            pdPackagingStructureList.get(i).setIsRoot(1);
            pdPackagingStructureList.get(i).setIsLeaf(0);
            pdPackagingStructureList.get(i).setPackagingCount(pdPackagingStructureList.get(i).getProductCount());
            pdPackagingStructureList.get(i).setLayersCount(pdPackagingStructureList.size());
            pdPackagingStructureList.get(i).setMaterialCount(pdPackagingStructureList.get(i).getItemList().size() + 1);
            pdPackagingStructureList.get(i).setEnabled(1);
            pdPackagingStructureList.get(i).setEnabledTime(info.getCreateDate());
            pdPackagingStructureList.get(i).setCreateDate(info.getCreateDate());
            pdPackagingStructureList.get(i).setCreateName(info.getCreateName());
            pdPackagingStructureList.get(i).setCreator(info.getCreator());


            if (i == 0) {
                // pdPackagingStructureList.get(i).setParent(0);
                //添加产品重量
                setNetWeight(pdPackagingStructureList.get(i), pdBase, null, null);
                //添加包装重量
                setGrossWeight(pdPackagingStructureList.get(i));
            } else {
                // pdPackagingStructureList.get(i).setParent(pdPackagingStructureList.get(i-1).getId());
                //添加产品重量
                setNetWeight(pdPackagingStructureList.get(i), pdBase, pdPackagingStructureList.get(i - 1).getNetWeight(), pdPackagingStructureList.get(i - 1).getNetUnitId());
                //添加包装重量
                setGrossWeight(pdPackagingStructureList.get(i));
            }
        }

        if (pdPackagingStructureList.size() > 0) {
            //计算产品净重
            info.setNetWeight(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getNetWeight());
            info.setNetUnitId(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getNetUnitId());
            info.setNetUnit(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getNetUnit());

            info.setGrossWeight(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getGrossWeight());
            info.setGrossUnitId(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getGrossUnitId());
            info.setGrossUnit(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getGrossUnit());

            info.setTotalWeight(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getTotalWeight());
            info.setTotalUnitId(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getTotalUnitId());
            info.setTotalUnit(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getTotalUnit());

            info.setProductCount(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getProductCount());
            info.setPackagingCount(pdPackagingStructureList.get(pdPackagingStructureList.size() - 1).getPackagingCount());


        }

        pdPackagingInfoDao.update(info);
        PdPackagingInfoHistory infoHistory = pdPackagingInfoHistoryDao.insert(info);

        for (PdPackagingStructure pdPackagingStructure : pdPackagingStructureList) {
            pdPackagingStructure.setPackaging(info.getId());
            pdPackagingStructureDao.save(pdPackagingStructure);
            PdPackagingStructureHistory packagingStructureHistory = pdPackagingStructureHistoryDao.insert(pdPackagingStructure, infoHistory.getId());

            for (PdPackagingItem item : pdPackagingStructure.getItemList()
            ) {
                item.setPackagingStruct(pdPackagingStructure.getId());
                MtBase mtBase = mtBaseDao.get(item.getMaterial());
                mtBase.setOperation(null);
                int baseHistoryId = mtBaseHistoryDao.insert(mtBase);


                pdPackagingItemDao.save(item);
                pdPackagingItemHistoryDao.insert(item, packagingStructureHistory.getId(), baseHistoryId);

            }

            pdPackagingStructure.getZyPackaging().setPackagingStruct(pdPackagingStructure.getId());
            pdPackagingItemDao.save(pdPackagingStructure.getZyPackaging());

            MtBase mtBase = mtBaseDao.get(pdPackagingStructure.getZyPackaging().getMaterial());
            if (mtBase != null) {
                mtBase.setOperation(null);
            }

            int baseHistoryId = mtBaseHistoryDao.insert(mtBase);
            pdPackagingItemHistoryDao.insert(pdPackagingStructure.getZyPackaging(), packagingStructureHistory.getId(), baseHistoryId);

        }


        return "success";
    }

    @Override
    public String updatePackagingInfo(PdPackagingInfo info) {

        pdPackagingInfoDao.update(info);
        return "success";
    }

    @Override
    public PdPackagingInfo getPackagingDetail(Integer id) {
        PdPackagingInfo info = pdPackagingInfoDao.get(id);
        Map<String, Object> map = new HashMap<>();
        map.put("packaging", id);
        List<PdPackagingStructure> structureList = pdPackagingStructureDao.getListByHQLWithNamedParams("from PdPackagingStructure where packaging=:packaging and enabled=1", map);

        for (PdPackagingStructure pps : structureList) {

            if (pps.getNetWeight() != null && pps.getNetWeight().compareTo(BigDecimal.ZERO) == 0) {
                pps.setNetWeight(null);
                pps.setNetUnit(null);
                pps.setNetUnitId(null);
            }

            if (pps.getGrossWeight() != null && pps.getGrossWeight().compareTo(BigDecimal.ZERO) == 0) {
                pps.setGrossWeight(null);
                pps.setGrossUnit(null);
                pps.setGrossUnitId(null);
            }

            if (pps.getTotalWeight() != null && pps.getTotalWeight().compareTo(BigDecimal.ZERO) == 0) {
                pps.setTotalWeight(null);
                pps.setTotalUnit(null);
                pps.setTotalUnitId(null);
            }

            Map<String, Object> param = new HashMap<>();
            param.put("packagingStruct", pps.getId());
            List<PdPackagingItem> itemList = pdPackagingItemDao.getListByHQLWithNamedParams("from PdPackagingItem where packagingStruct=:packagingStruct  and enabled=1", param);

            List<Integer> itemIds = itemList.stream()
                    .map(PdPackagingItem::getMaterial)
                    .collect(Collectors.toList());
            String ids = StringUtils.join(itemIds, ",");

            List<MtBase> mtBases = pdBaseDao.getListByHQLWithNamedParams("from MtBase where id in (" + ids + ")", new HashMap<>());


            List<PdPackagingItem> l = new ArrayList<>();
            for (PdPackagingItem pdPackagingItem : itemList) {

                for (MtBase mtBase : mtBases
                ) {
                    if (pdPackagingItem.getMaterial().intValue() == mtBase.getId()) {
                        pdPackagingItem.setName(mtBase.getName());
                        pdPackagingItem.setCode(mtBase.getCode());
                        break;
                    }
                }

                if (pdPackagingItem.getIsPrincipal().intValue() == 1) {
                    pps.setZyPackaging(pdPackagingItem);
                } else {
                    l.add(pdPackagingItem);
                }
            }
            pps.setItemList(l);

        }
        info.setStructureList(structureList);
        return info;
    }

    @Override
    public PdPackagingInfo getPackaging(Integer id) {
        return pdPackagingInfoDao.get(id);
    }

    @Override
    public List<PdPackagingInfoHistory> getPackagingList(Integer productId) {

        PdMerchandise pdMerchandise = pdMerchandiseDao.get(productId);

        Map<String, Object> map = new HashMap<>();
        map.put("merchandise", productId);


        List<PdPackagingInfoHistory> allList = pdPackagingInfoDao.getListByHQLWithNamedParams("from PdPackagingInfoHistory where id in (select max(sh.id) from PdPackagingInfoHistory as sh where sh.merchandise=:merchandise  and sh.effectTime <= current_timestamp() group by sh.packaging ) ", map);

        Map<String, Object> param1 = new HashMap<>();
        param1.put("product", productId);
        List<PdPackagingStructureHistory> structureList = pdPackagingStructureDao.getListByHQLWithNamedParams("from PdPackagingStructureHistory where product=:product and enabled=1", param1);


        for (PdPackagingInfoHistory info : allList) {


            List<PdPackagingStructureHistory> structures = new ArrayList<>();
            for (PdPackagingStructureHistory pps : structureList) {


                if (pps.getNetWeight() != null && pps.getNetWeight().compareTo(BigDecimal.ZERO) == 0) {
                    pps.setNetWeight(null);
                    pps.setNetUnit(null);
                    pps.setNetUnitId(null);
                }

                if (pps.getGrossWeight() != null && pps.getGrossWeight().compareTo(BigDecimal.ZERO) == 0) {
                    pps.setGrossWeight(null);
                    pps.setGrossUnit(null);
                    pps.setGrossUnitId(null);
                }

                if (pps.getTotalWeight() != null && pps.getTotalWeight().compareTo(BigDecimal.ZERO) == 0) {
                    pps.setTotalWeight(null);
                    pps.setTotalUnit(null);
                    pps.setTotalUnitId(null);
                }

                if (pps.getPackaging().intValue() == info.getId()) {
                    Map<String, Object> param = new HashMap<>();
                    param.put("packagingStruct", pps.getId());
                    List<PdPackagingItemHistory> itemList = pdPackagingItemDao.getListByHQLWithNamedParams("from PdPackagingItemHistory where packagingStruct=:packagingStruct  and enabled=1", param);

                    List<Integer> itemIds = itemList.stream()
                            .map(PdPackagingItemHistory::getMaterial)
                            .collect(Collectors.toList());
                    String ids = StringUtils.join(itemIds, ",");

                    List<MtBaseHistory> mtBases = pdBaseDao.getListByHQLWithNamedParams("from MtBaseHistory where id in (" + ids + ")", new HashMap<>());


                    List<PdPackagingItemHistory> l = new ArrayList<>();
                    for (PdPackagingItemHistory pdPackagingItem : itemList) {

                        PdPackagingItemHistory pi = new PdPackagingItemHistory();
                        BeanUtils.copyProperties(pdPackagingItem, pi);

                        for (MtBaseHistory mtBase : mtBases
                        ) {
                            if (pdPackagingItem.getMaterial().intValue() == mtBase.getId()) {


                                pi.setName(mtBase.getName());
                                pi.setCode(mtBase.getCode());
                                pi.setSpecifications(mtBase.getSpecifications());
                                pi.setModel(mtBase.getModel());
                                pi.setUnit(mtBase.getUnit());
                                pi.setUnitId(mtBase.getUnitId());
                                pi.setMaterial(mtBase.getMaterial());


                                break;
                            }
                        }
                        if (pi.getIsPrincipal() == 1) {
                            pps.setZyPackaging(pi);
                        } else {
                            l.add(pi);
                        }
                    }
                    pps.setItemList(l);
                    structures.add(pps);
                }
            }

            info.setStructureList(structures);
            info.setUnit(pdMerchandise.getUnit());
        }


        return allList;
    }

    @Override
    public List<PdPackagingInfoHistory> getPackagingRecordList(Integer id) {
        String hql = "from PdPackagingInfoHistory where packaging=:packaging";
        Map<String, Object> param = new HashMap<>();
        param.put("packaging", id);
        return pdPackagingInfoHistoryDao.getListByHQLWithNamedParams(hql, param);
    }

    @Override
    public PdPackagingInfoHistory getPackagingRecordDetail(Integer recordId) {
        PdPackagingInfoHistory infoHistory = pdPackagingInfoHistoryDao.get(recordId);
        Map<String, Object> map = new HashMap<>();
        map.put("packaging", recordId);
        List<PdPackagingStructureHistory> structureList = pdPackagingStructureDao.getListByHQLWithNamedParams("from PdPackagingStructureHistory where packaging=:packaging and enabled=1", map);

        for (PdPackagingStructureHistory pps : structureList) {

            if (pps.getNetWeight() != null && pps.getNetWeight().compareTo(BigDecimal.ZERO) == 0) {
                pps.setNetWeight(null);
                pps.setNetUnit(null);
                pps.setNetUnitId(null);
            }

            if (pps.getGrossWeight() != null && pps.getGrossWeight().compareTo(BigDecimal.ZERO) == 0) {
                pps.setGrossWeight(null);
                pps.setGrossUnit(null);
                pps.setGrossUnitId(null);
            }

            if (pps.getTotalWeight() != null && pps.getTotalWeight().compareTo(BigDecimal.ZERO) == 0) {
                pps.setTotalWeight(null);
                pps.setTotalUnit(null);
                pps.setTotalUnitId(null);
            }


            Map<String, Object> param = new HashMap<>();
            param.put("packagingStruct", pps.getId());
            List<PdPackagingItemHistory> itemList = pdPackagingItemDao.getListByHQLWithNamedParams("from PdPackagingItemHistory where packagingStruct=:packagingStruct  and enabled=1", param);

            List<Integer> itemIds = itemList.stream()
                    .map(PdPackagingItemHistory::getMaterial)
                    .collect(Collectors.toList());
            String ids = StringUtils.join(itemIds, ",");

            List<MtBaseHistory> mtBases = pdBaseDao.getListByHQLWithNamedParams("from MtBaseHistory where id in (" + ids + ")", new HashMap<>());


            List<PdPackagingItemHistory> l = new ArrayList<>();
            for (PdPackagingItemHistory pdPackagingItem : itemList) {
                PdPackagingItemHistory pi = new PdPackagingItemHistory();
                BeanUtils.copyProperties(pdPackagingItem, pi);
                for (MtBaseHistory mtBase : mtBases
                ) {
                    if (pdPackagingItem.getMaterial().intValue() == mtBase.getId()) {
                        pi.setName(mtBase.getName());
                        pi.setCode(mtBase.getCode());
                        pi.setSpecifications(mtBase.getSpecifications());
                        pi.setModel(mtBase.getModel());
                        pi.setUnit(mtBase.getUnit());
                        pi.setUnitId(mtBase.getUnitId());
                        pi.setMaterial(mtBase.getMaterial());
                        break;
                    }
                }

                if (pi.getIsPrincipal().intValue() == 1) {
                    pps.setZyPackaging(pi);
                } else {
                    l.add(pi);
                }
            }
            pps.setItemList(l);

        }
        infoHistory.setStructureList(structureList);
        return infoHistory;
    }

    @Override
    public List<PdPackagingInfo> getWxPackagingList(Integer productId) {

        Map<String, Object> map = new HashMap<>();
        map.put("merchandise", productId);

        List<PdPackagingInfo> allList = pdPackagingInfoDao.getListByHQLWithNamedParams("from PdPackagingInfo where merchandise=:merchandise and layersCount=0  and  enabled=1", map);

        return allList;
    }

    //获取包装数量
    private BigDecimal getPackagingCount(List<PdPackagingStructure> list) {


        return null;
    }


    //添加产品重量
    private void setNetWeight(PdPackagingStructure pdPackagingStructure, PdBase pdBase, BigDecimal parentNetWeight, Integer parentWeightUnit) {
        BigDecimal a = new BigDecimal(0);
        if (parentNetWeight == null) {
            a = UnitUnit.toMilligram(pdBase.getNetWeight().multiply(new BigDecimal(pdPackagingStructure.getProductCount())), pdBase.getWeightUnit());
        } else {
            a = UnitUnit.toMilligram(parentNetWeight, parentWeightUnit + "").multiply(new BigDecimal(pdPackagingStructure.getProductCount()));

        }

        pdPackagingStructure.setNetWeight(UnitUnit.milligramToBest(a));
        pdPackagingStructure.setNetUnitId(Integer.valueOf(UnitUnit.milligramToUnit(a)));
        pdPackagingStructure.setNetUnit(UnitUnit.milligramToUnitName(a));
    }

    //添加包装毛重
    private void setGrossWeight(PdPackagingStructure pdPackagingStructure) {
        //获取辅助包装
        List<PdPackagingItem> items = pdPackagingStructure.getItemList();

        //辅助材料总重量
        BigDecimal sum = new BigDecimal(0);
        if (items != null) {
            for (PdPackagingItem item : items) {
                item.setOrg(pdPackagingStructure.getOrg());
                item.setCreateDate(pdPackagingStructure.getCreateDate());
                item.setCreateName(pdPackagingStructure.getCreateName());
                item.setCreator(pdPackagingStructure.getCreator());
                item.setPackagingStruct(pdPackagingStructure.getId());
                item.setEnabled(1);
                item.setEnabledTime(pdPackagingStructure.getCreateDate());
                item.setIsPrincipal(0);

                if (item.getStockWeight() == null) {
                    item.setStockWeight(new BigDecimal(0));
                }
                if (item.getStockWeightUnitId() == null) {
                    item.setStockWeightUnitId(1);
                }


                BigDecimal a = UnitUnit.toMilligram(item.getStockWeight().multiply(item.getRatedAmout()), item.getStockWeightUnitId() + "");

                sum = sum.add(a);

//                item.setStockWeight(UnitUnit.milligramToBest(a));
//                item.setStockWeightUnitId(Integer.valueOf(UnitUnit.milligramToUnit(a)));
//                item.setStockWeightUnit(UnitUnit.milligramToUnitName(a));


            }
        }

        //计算主要材料毛重
        PdPackagingItem zy = pdPackagingStructure.getZyPackaging();

        if (zy.getStockWeight() == null) {
            zy.setStockWeight(new BigDecimal(0));
        }
        if (zy.getStockWeightUnitId() == null) {
            zy.setStockWeightUnitId(1);
        }

        BigDecimal a = UnitUnit.toMilligram(zy.getStockWeight().multiply(zy.getRatedAmout()), zy.getStockWeightUnitId() + "");
        sum = sum.add(a);
//        zy.setStockWeight(UnitUnit.milligramToBest(a));
//        zy.setStockWeightUnitId(Integer.valueOf(UnitUnit.milligramToUnit(a)));
//        zy.setStockWeightUnit(UnitUnit.milligramToUnitName(a));
        zy.setOrg(pdPackagingStructure.getOrg());
        zy.setCreateDate(pdPackagingStructure.getCreateDate());
        zy.setCreateName(pdPackagingStructure.getCreateName());
        zy.setCreator(pdPackagingStructure.getCreator());
        zy.setPackagingStruct(pdPackagingStructure.getId());
        zy.setEnabled(1);
        zy.setEnabledTime(pdPackagingStructure.getCreateDate());
        zy.setIsPrincipal(1);


        pdPackagingStructure.setGrossWeight(UnitUnit.milligramToBest(sum));
        pdPackagingStructure.setGrossUnitId(Integer.valueOf(UnitUnit.milligramToUnit(sum)));
        pdPackagingStructure.setGrossUnit(UnitUnit.milligramToUnitName(sum));

        BigDecimal s = UnitUnit.toMilligram(pdPackagingStructure.getNetWeight(), pdPackagingStructure.getNetUnitId() + "").add(UnitUnit.toMilligram(pdPackagingStructure.getGrossWeight(), pdPackagingStructure.getGrossUnitId() + ""));
        pdPackagingStructure.setTotalWeight(UnitUnit.milligramToBest(s));
        pdPackagingStructure.setTotalUnitId(Integer.valueOf(UnitUnit.milligramToUnit(s)));
        pdPackagingStructure.setTotalUnit(UnitUnit.milligramToUnitName(s));


    }

}
